{"version": "0.2.0", "configurations": [{"name": "start dev", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "env": {"GIT_PKG_ACCESS_TOKEN": "", "FORCE_COLOR": "1"}, "runtimeArgs": ["run", "start-dev"], "smartStep": true, "outputCapture": "std", "cwd": "${workspaceFolder}/apps/bm-projects-back/"}, {"name": "debug task api dev", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "node", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "args": ["-r", "${workspaceFolder}/apps/bm-projects-back/src/env.ts", "-r", "${workspaceFolder}/apps/bm-projects-back/src/di.ts", "${file}"], "smartStep": true, "outputCapture": "std", "cwd": "${workspaceFolder}/apps/bm-projects-back", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}, {"name": "start dev (debug backend)", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "node", "runtimeArgs": ["--inspect", "--<PERSON><PERSON><PERSON>", "-r", "ts-node/register"], "args": ["-r", "${workspaceFolder}/apps/bm-projects-back/src/env.ts", "${workspaceFolder}/apps/bm-projects-back/src/main.ts"], "env": {"NODE_ENV": "development", "FORCE_COLOR": "1", "TS_NODE_PROJECT": "${workspaceFolder}/apps/bm-projects-back/tsconfig.app.json", "TS_NODE_COMPILER_OPTIONS": "{\"module\":\"commonjs\"}"}, "cwd": "${workspaceFolder}/apps/bm-projects-back", "console": "integratedTerminal", "smartStep": true, "outputCapture": "std", "preLaunchTask": "start frontend"}]}