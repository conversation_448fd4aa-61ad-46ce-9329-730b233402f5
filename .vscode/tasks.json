{"version": "2.0.0", "tasks": [{"label": "start frontend", "type": "shell", "command": "pnpm", "args": ["nx", "serve", "bm-projects-front", "--configuration=development"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": {"owner": "webpack-dev-server", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".*webpack.*compiling.*", "endsPattern": ".*(compiled successfully|Failed to compile).*"}}}]}