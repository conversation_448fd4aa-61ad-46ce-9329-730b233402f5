{"name": "bm-projects-back-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["bm-projects-back"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/bm-projects-back-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["bm-projects-back:build"]}}}