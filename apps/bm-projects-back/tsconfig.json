{
  "extends": "../../tsconfig.base.json",
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ],
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  },
  "compilerOptions": {
    "esModuleInterop": true,
    "resolveJsonModule": true,
    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/",

    "baseUrl": "./src",
    "paths": {
      ":env": ["env.ts"],
      ":config": ["config.ts"],
      ":main": ["./src/main.ts"],
      ":helpers/*": ["helpers/*"],
      ":shared/*": ["shared/*"],
      ":modules/*": ["modules/*"],
      ":middlewares/*": ["middlewares/*"]
    }
  }
}
