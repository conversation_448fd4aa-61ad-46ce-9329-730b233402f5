import { inject, registry, singleton } from "tsyringe";
import { IDbPort } from "./db.interface";
import { InMemoryDB } from "./adapters/in-memory";
import { MongoDb } from "./adapters/mongodb";
import { Config } from "../config";

export const DB_PORT_TOKEN = "IDbPort";

@registry([
  {
    token: DB_PORT_TOKEN,
    useClass: useAdapterAccordingToConfig(),
  },
])
@singleton()
export class DB {
  constructor(@inject(DB_PORT_TOKEN) private readonly _dbPort?: IDbPort) {}

  async start(): Promise<void> {
    await this._dbPort?.start();
  }

  async end(): Promise<void> {
    await this._dbPort?.end();
  }
}

function useAdapterAccordingToConfig(): typeof MongoDb | typeof InMemoryDB {
  switch (Config.db.type) {
    case "mongodb":
      return MongoDb;
    default:
      return InMemoryDB;
  }
}
