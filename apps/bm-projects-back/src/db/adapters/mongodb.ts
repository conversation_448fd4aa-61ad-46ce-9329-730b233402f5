import { logger } from ":helpers/logger";
import { IDbPort } from "../db.interface";
import { DateTime } from "luxon";
import mongoose from "mongoose";

export class MongoDb implements IDbPort {
  async start() {
    const now = DateTime.now();
    logger.info("MongoDB - starting...");

    // Mongoose configuration
    mongoose.set("debug", String(process.env.DEBUG_MONGO) === "true");

    mongoose.set("strict", true); // allow no extra fields inserted without throwing error
    mongoose.set("strictQuery", false); // allow extra fields in queries
    mongoose.set("runValidators", true); // run validators on update

    // Connect database
    const autoIndex = process.env.NODE_ENV !== "production";
    const mongoURI = process.env.MONGODB_URI ?? "";
    mongoose.connect(mongoURI, { autoIndex, serverSelectionTimeoutMS: 60000 });

    // CONNECTION EVENTS
    // When successfully connected
    mongoose.connection.on("connected", function () {
      logger.info(
        `MongoDB - started in ${DateTime.now()
          .diff(now)
          .as("milliseconds")} milliseconds`
      );
    });

    // If the connection throws an error
    mongoose.connection.on("error", function (err) {
      logger.error(`Mongoose default connection error: ${err}`);
    });

    // When the connection is disconnected
    mongoose.connection.on("disconnected", function () {
      logger.info("MongoDB - disconnected successfully");
    });
  }

  async end() {
    logger.info("MongoDB - disconnecting...");
    await mongoose.connection.close(true);
  }
}
