import 'reflect-metadata';

import './env';

import bodyParser from 'body-parser';
import chalk from 'chalk';
import cors from 'cors';
import express, { Application } from 'express';

import { Config } from ':config';
import { checkConfigAndLogValidationErrors } from ':helpers/config.checker';
import { errorHandlerMiddleware } from ':middlewares/error-handler.middleware';
import { logger } from ':helpers/logger';

import { api } from './api';
import { DB } from './db/db.port';
import { container } from 'tsyringe';
import { exitProcess } from ':helpers/exit-process';

checkConfigAndLogValidationErrors();
initApplication();

export async function initApplication(): Promise<Application> {
  // Start database
  const db = container.resolve(DB);
  await db.start();

  const app: Application = express();

  logger.info(
    `Starting server with NODE_ENV = ${chalk.bold.cyan(process.env.NODE_ENV)}`
  );

  const allowedOrigins: string[] = [];
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.NODE_ENV === 'local'
  ) {
    allowedOrigins.push('https://*.cloudfront.net');
    allowedOrigins.push('http://localhost:4200');
  }

  // Cors
  app.use(
    cors({
      origin: allowedOrigins,
      credentials: true,
    })
  );
  app.options('*', cors());

  // Body Parser
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(
    bodyParser.urlencoded({
      limit: '50mb',
      extended: true,
    })
  );

  // Health check
  app.get('/amen', function (req, res) {
    res.json({
      name: 'bm-projects-back',
      version: '1.0.0',
      msg: 'Amen to that boys.',
    });
  });

  app.get('/', function (req, res) {
    res.json({
      msg: 'Server Up',
    });
  });

  // API
  app.use(Config.settings.apiRoute.v1, api());

  // Error handling
  app.use(errorHandlerMiddleware);

  // Optional fallthrough error handler
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  app.use(function onError(err: any, req: any, res: any, next: any) {
    // The error id is attached to `res.sentry` to be returned
    // and optionally displayed to the user for support.
    res.statusCode = 500;
    res.end(`${res.sentry}\n`);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error(`Unhandled Rejection`, {
      app: 'api',
      reason,
      promise: JSON.stringify(promise),
    });
    exitProcess(1);
  });

  process.on('uncaughtException', (err, origin) => {
    logger.error(`Uncaught Exception`, {
      app: 'api',
      err,
      origin,
    });
    exitProcess(1);
  });

  ['SIGINT', 'SIGTERM'].forEach((signal) =>
    process.on(signal, () => {
      logger.info('Process exited on signal', {
        app: 'api',
        signal,
      });
      exitProcess(0);
    })
  );

  // Run server
  const port = process.env.PORT ?? 3000;
  app.set('port', port);

  if (!['tests', 'local-tests'].includes(process.env.NODE_ENV ?? '')) {
    app.listen(port, () => logger.info(`Running on localhost:${port}`));
  }

  return app;
}
