export const Config = {
  db: {
    type: process.env.DB_TYPE ?? "in-memory",
  },
  jwt: {
    secretKey: process.env.JWT_SECRET ?? "",
  },
  logger: {
    level: process.env.LOG_LEVEL ?? "info",
  },
  oauth: {
    clientId: process.env.GOOGLE_CLIENT_ID ?? "",
    clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    redirectUri: process.env.GOOGLE_REDIRECT_URI ?? "",
  },
  settings: {
    apiRoute: {
      v1: "/api/v1",
    },
    webBaseUrl: {
      v1: process.env.WEB_BASE_URL ?? "http://localhost:4200",
    },
  },
};
