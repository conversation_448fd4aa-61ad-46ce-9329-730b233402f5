import path from "path";

console.info(
  "WARNING ! Don't forget to import src/env.ts before any package models import"
);
if (!process.env.NODE_ENV) {
  throw new Error("!!! NO ENVIRONMENT SET !!!");
}

require("dotenv").config({
  path: path.resolve(__dirname, `../.env.${process.env.NODE_ENV}`),
});
require("dotenv").config({
  path: path.resolve(__dirname, "../.env.default"),
});

console.log(`Running on env: ${process.env.NODE_ENV}`);
