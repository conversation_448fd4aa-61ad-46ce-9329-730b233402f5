import express, { Router } from "express";
import { container } from "tsyringe";
import AuthRouter from "./modules/auth/auth.router";
import UsersRouter from "./modules/users/users.router";

export const api = (): Router => {
  const router = express.Router();

  router.use("/auth", container.resolve(AuthRouter).init());
  router.use("/users", container.resolve(UsersRouter).init());

  return router;
};
