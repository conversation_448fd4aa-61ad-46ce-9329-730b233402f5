import { Config } from ':config';
import { Body, Query } from ':helpers/decorators/request-validators';
import {
  GoogleCallbackQueryDto,
  googleCallbackQueryValidator,
  IsTokenValidBodyDto,
  isTokenValidBodyValidator,
} from ':helpers/dtos/auth/auth.requests.dto';
import GoogleCallbackUseCase from ':modules/auth/use-cases/google-callback.use-case';
import { ApiResult } from '@bm-projects-monorepo/utils';
import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';
import { GetGoogleRedirectUrlUseCase } from './use-cases/get-google-redirect-url.use-case';
import IsTokenValidUseCase from './use-cases/is-token-valid.use-case';

@singleton()
export default class AuthController {
  constructor(
    private readonly _getGoogleRedirectUrlUseCase: GetGoogleRedirectUrlUseCase,
    private readonly _googleCallbackUseCase: GoogleCallbackUseCase,
    private readonly _isTokenValidUseCase: IsTokenValidUseCase
  ) {}

  handleAuthGoogle(
    _req: Request,
    res: Response<ApiResult<{ redirectUrl: string }>>,
    next: NextFunction
  ) {
    try {
      const redirectUrl = this._getGoogleRedirectUrlUseCase.execute();
      res.json({ data: { redirectUrl } });
    } catch (error) {
      next(error);
    }
  }

  @Query(googleCallbackQueryValidator)
  async handleGoogleCallback(
    req: Request<never, never, never, GoogleCallbackQueryDto>,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { code } = req.query;

      const { accessToken, cookieOptions } =
        await this._googleCallbackUseCase.execute(code as string);

      res.cookie('accessToken', accessToken, cookieOptions);
      res.redirect(Config.settings.webBaseUrl.v1);
    } catch (error) {
      next(error);
    }
  }

  @Body(isTokenValidBodyValidator)
  handleIsTokenValid(
    req: Request<never, never, IsTokenValidBodyDto>,
    res: Response<ApiResult<boolean>>,
    next: NextFunction
  ) {
    try {
      const { accessToken } = req.body;
      const isValid = this._isTokenValidUseCase.execute(accessToken);
      res.json({ data: isValid });
    } catch (error) {
      next(error);
    }
  }
}
