import { inject, singleton } from "tsyringe";
import { UsersRepositoryPort } from ":modules/users/repository/users.repository";
import { User } from ":modules/users/entities/users.entity";
import { JwtService } from ":helpers/services/jwt/jwt.service";
import { CookieOptions } from "express";
import { IdService } from ":helpers/services/id/id.service";
import { CustomError, CustomErrorCode } from ":helpers/errors/custom-error";
import { OAuth2Service } from ":helpers/services/oauth2/oauth2.service";
import { OAuth2UserProfile } from ":helpers/services/oauth2/oauth2.interface";
import { Role } from ":shared/enums/role";
import { userEmailValidator } from ":modules/users/validators/users.validator";

@singleton()
export default class GoogleCallbackUseCase {
  constructor(
    private readonly _idService: IdService,
    private readonly _jwtService: JwtService,
    private readonly _oAuth2Service: OAuth2Service,
    private readonly _usersRepository: UsersRepositoryPort
  ) {}

  async execute(
    code: string
  ): Promise<{ accessToken: string; cookieOptions: CookieOptions }> {
    // Exchange authorization code for access token
    const accessToken = await this._oAuth2Service.getOAuth2Token(code);

    // Use access_token to fetch user profile
    const profile = await this._oAuth2Service.getUserProfile(accessToken);

    // Get user or create user
    const user = await this._findOrCreateUser(profile);
    if (!user.active) {
      throw new CustomError(CustomErrorCode.USER_NOT_ACTIVE, {
        metadata: { email: user.email },
        message: "User is not active",
      });
    }

    // Generate JWT token
    const twoMinutesInMilliseconds = 2 * 60 * 1000;
    const jwt = this._jwtService.generate({
      userId: user.id,
      roles: user.roles,
    });
    return {
      accessToken: jwt,
      cookieOptions: { maxAge: twoMinutesInMilliseconds },
    };
  }

  private async _findOrCreateUser(profile: OAuth2UserProfile): Promise<User> {
    try {
      userEmailValidator.parse(profile.email);
    } catch (error) {
      throw new CustomError(CustomErrorCode.UNAUTHORIZED_EMAIL_DOMAIN, {
        metadata: { email: profile.email, error },
        message: "Unauthorized email domain",
      });
    }

    const user = await this._usersRepository.findUserByEmail(profile.email);
    if (user) {
      return user;
    }

    const newUser = new User({
      id: this._idService.generate(),
      email: profile.email,
      firstname: profile.given_name,
      lastname: profile.family_name,
      active: true,
      roles: [Role.ADMIN], // TODO Role.USER
    });

    return this._usersRepository.createUser(newUser);
  }
}
