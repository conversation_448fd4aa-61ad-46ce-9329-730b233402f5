import { Router, Request, Response } from 'express';
import { singleton } from 'tsyringe';
import AbstractRouter from ':helpers/abstract.router';
import AuthController from './auth.controller';
import {
  GoogleCallbackQueryDto,
  IsTokenValidBodyDto,
} from ':helpers/dtos/auth/auth.requests.dto';

@singleton()
export default class AuthRouter extends AbstractRouter {
  constructor(private readonly _authController: AuthController) {
    super();
  }

  init(): Router {
    this.router.get('/google/login', (req, res, next) =>
      this._authController.handleAuthGoogle(req, res, next)
    );

    this.router.get(
      '/google/callback',
      (
        req: Request<never, never, never, GoogleCallbackQueryDto>,
        res: Response<any>,
        next
      ) => this._authController.handleGoogleCallback(req, res, next)
    );

    this.router.post(
      '/is-token-valid',
      (
        req: Request<never, never, IsTokenValidBodyDto>,
        res: Response<any>,
        next
      ) => this._authController.handleIsTokenValid(req, res, next)
    );

    return this.router;
  }
}
