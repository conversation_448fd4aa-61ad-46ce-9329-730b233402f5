import AbstractRouter from ':helpers/abstract.router';
import { RequestWithUser } from ':helpers/utils/utility-types';
import { authorize } from ':middlewares/authorize';
import UsersController from ':modules/users/users.controller';
import { Role } from ':shared/enums/role';
import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

@singleton()
export default class UsersRouter extends AbstractRouter {
  constructor(private readonly _usersController: UsersController) {
    super();
  }

  init(): Router {
    this.router.get(
      '/me',
      authorize(),
      (req: Request, res: Response<any>, next: NextFunction) =>
        this._usersController.handleGetLoggedInUser(
          req as RequestWithUser,
          res,
          next
        )
    );

    this.router.get(
      '',
      authorize([Role.ADMIN]),
      (req: Request, res: Response<any>, next: NextFunction) =>
        this._usersController.handleGetUsers(req, res, next)
    );

    return this.router;
  }
}
