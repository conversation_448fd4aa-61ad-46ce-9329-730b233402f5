import "reflect-metadata";
import { container } from "tsyringe";
import { UsersRepositoryPort } from ":modules/users/repository/users.repository";
import GetUserByIdUseCase from ":modules/users/use-cases/get-user-by-id.use-case";
import { User } from ":modules/users/entities/users.entity";
import { IdService } from ":helpers/services/id/id.service";
import { Role } from ":shared/enums/role";
import { CustomErrorCode } from ":helpers/errors/custom-error";

describe("GetUserByIdUseCase", () => {
  afterEach(() => {
    container.reset();
  });

  it("should return the user", async () => {
    // arrange
    const idService = container.resolve(IdService);
    const usersRepository = container.resolve(UsersRepositoryPort);
    const getUserByIdUseCase = container.resolve(GetUserByIdUseCase);

    const expectedUser = new User({
      id: idService.generate(),
      email: "<EMAIL>",
      firstname: "<PERSON>",
      lastname: "<PERSON><PERSON>",
      active: true,
      roles: [Role.USER],
    });
    await usersRepository.createUser(expectedUser);

    // act
    const user = await getUserByIdUseCase.execute(expectedUser.id);

    // assert
    expect(user).toStrictEqual(expectedUser.toDto());
  });

  it("should throw an error", async () => {
    // arrange
    const idService = container.resolve(IdService);
    const getUserByIdUseCase = container.resolve(GetUserByIdUseCase);
    const userId = idService.generate();
    const expectedErrorCode = CustomErrorCode.USER_NOT_FOUND;

    // act & assert
    await expect(getUserByIdUseCase.execute(userId)).rejects.toThrow(
      expect.objectContaining({
        customErrorCode: expectedErrorCode,
      })
    );
  });
});
