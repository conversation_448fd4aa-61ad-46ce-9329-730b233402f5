import "reflect-metadata";
import { container } from "tsyringe";
import { UsersRepositoryPort } from ":modules/users/repository/users.repository";
import { User } from ":modules/users/entities/users.entity";
import { IdService } from ":helpers/services/id/id.service";
import { Role } from ":shared/enums/role";
import { GetUsersUseCase } from ":modules/users/use-cases/get-users.use-case";

describe("GetUsersUseCase", () => {
  afterEach(() => {
    container.reset();
  });

  it("should return the users", async () => {
    // arrange
    const idService = container.resolve(IdService);
    const usersRepository = container.resolve(UsersRepositoryPort);
    const getUsersUseCase = container.resolve(GetUsersUseCase);

    const expectedUsers: User[] = [
      new User({
        id: idService.generate(),
        email: "<EMAIL>",
        firstname: "<PERSON>",
        lastname: "<PERSON><PERSON>",
        active: true,
        roles: [Role.USER],
      }),
      new User({
        id: idService.generate(),
        email: "<EMAIL>",
        firstname: "<PERSON>",
        lastname: "<PERSON>",
        active: true,
        roles: [Role.USER, Role.ADMIN],
      }),
      new User({
        id: idService.generate(),
        email: "<EMAIL>",
        firstname: "Gilbert",
        lastname: "Mason",
        active: false,
        roles: [Role.USER],
      }),
    ];
    const promises = expectedUsers.map((user) =>
      usersRepository.createUser(user)
    );
    await Promise.all(promises);

    // act
    const users = await getUsersUseCase.execute();

    // assert
    // TODO expected users should be the DTOs directly + fix filename of the test
    expect(users).toIncludeSameMembers(
      expectedUsers.map((user) => user.toDto())
    );
  });
});
