import { UserDto } from ':helpers/dtos/users/users.dto';
import { CustomError, CustomErrorCode } from ':helpers/errors/custom-error';
import { UsersRepositoryPort } from ':modules/users/repository/users.repository';
import { singleton } from 'tsyringe';

// TODO one directory per use case, with the tests in the same directory
@singleton()
export default class GetUserByIdUseCase {
  constructor(private readonly _usersRepository: UsersRepositoryPort) {}

  async execute(userId: string): Promise<UserDto> {
    const user = await this._usersRepository.findUserById(userId);

    if (!user) {
      throw new CustomError(CustomErrorCode.USER_NOT_FOUND, {
        message: 'User not found',
        metadata: { userId },
      });
    }

    return user.toDto();
  }
}
