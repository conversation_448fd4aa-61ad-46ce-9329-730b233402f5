import { singleton } from 'tsyringe';
import { UsersRepositoryPort } from ':modules/users/repository/users.repository';
import { UserDto } from ':helpers/dtos/users/users.dto';

@singleton()
export class GetUsersUseCase {
  constructor(private readonly _usersRepository: UsersRepositoryPort) {}

  async execute(): Promise<UserDto[]> {
    const users = await this._usersRepository.findUsers();
    return users.map((user) => user.toDto());
  }
}
