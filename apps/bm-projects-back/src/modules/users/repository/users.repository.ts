import { inject, registry, singleton } from "tsyringe";
import { User } from ":modules/users/entities/users.entity";
import { IUsersRepository } from ":modules/users/repository/users.interface";
import { InMemoryUsersRepository } from ":modules/users/repository/adapters/in-memory/users.in-memory.repository";
import { Config } from ":config";
import { MongodbUsersRepository } from ":modules/users/repository/adapters/mongodb/users.mongodb.repository";

export const USERS_REPOSITORY_TOKEN = "IUsersRepository";

@registry([
  {
    token: USERS_REPOSITORY_TOKEN,
    useClass: useAdapterAccordingToConfig(),
  },
])
@singleton()
export class UsersRepositoryPort implements IUsersRepository {
  constructor(
    @inject(USERS_REPOSITORY_TOKEN)
    private readonly _usersRepositoryAdapter: IUsersRepository
  ) {}

  findUserByEmail(email: string): Promise<User | undefined> {
    return this._usersRepositoryAdapter.findUserByEmail(email);
  }

  findUserById(id: string): Promise<User | undefined> {
    return this._usersRepositoryAdapter.findUserById(id);
  }

  findUsers(): Promise<User[]> {
    return this._usersRepositoryAdapter.findUsers();
  }

  createUser(user: User): Promise<User> {
    return this._usersRepositoryAdapter.createUser(user);
  }
}

function useAdapterAccordingToConfig():
  | typeof InMemoryUsersRepository
  | typeof MongodbUsersRepository {
  switch (Config.db.type) {
    case "mongodb":
      return MongodbUsersRepository;
    default:
      return InMemoryUsersRepository;
  }
}
