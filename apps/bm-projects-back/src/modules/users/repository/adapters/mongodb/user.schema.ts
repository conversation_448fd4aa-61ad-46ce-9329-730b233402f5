import { JSONSchemaExtraProps } from ":helpers/mongoose/types";
import { Role } from ":shared/enums/role";

export const userJSONSchema = {
  $schema: "http://json-schema.org/draft-06/schema#",
  type: "object",
  additionalProperties: false,
  properties: {
    _id: {
      type: "string",
      format: "objectId",
    },
    id: {
      type: "string",
    },
    email: {
      type: "string",
    },
    firstname: {
      type: "string",
    },
    lastname: {
      type: "string",
    },
    active: {
      type: "boolean",
    },
    roles: {
      type: "array",
      items: {
        enum: Object.values(Role),
      },
    },
    createdAt: {
      type: "string",
      format: "date-time",
    },
    updatedAt: {
      type: "string",
      format: "date-time",
    },
  },
  required: [
    "id",
    "email",
    "firstname",
    "lastname",
    "active",
    "roles",
    "updatedAt",
    "createdAt",
  ],
  title: "User",
} as const satisfies JSONSchemaExtraProps;

export default userJSONSchema;
