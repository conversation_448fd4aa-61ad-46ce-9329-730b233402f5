import { FromSchema } from "json-schema-to-ts";
import mongoose from "mongoose";

import { DESERIALIZE_OPTIONS } from ":helpers/mongoose/constants";
import { createMongooseSchemaFromJSONSchema } from ":helpers/mongoose/jsonschema-to-mongoose";

import { userJSONSchema } from ":modules/users/repository/adapters/mongodb/user.schema";

const userSchema = createMongooseSchemaFromJSONSchema(userJSONSchema);

userSchema.index({ id: 1 }, { unique: true });

export type IUser = FromSchema<
  typeof userJSONSchema,
  {
    keepDefaultedPropertiesOptional: true;
    deserialize: DESERIALIZE_OPTIONS;
  }
>;

export const UserModel = mongoose.model<IUser>(
  userJSONSchema.title,
  userSchema
);
