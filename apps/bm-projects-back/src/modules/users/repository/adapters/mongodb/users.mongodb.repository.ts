import { singleton } from "tsyringe";
import { IUsersRepository } from ":modules/users/repository/users.interface";
import { User } from ":modules/users/entities/users.entity";
import { EntityRepository } from ":helpers/mongoose/entity.repository";
import {
  IUser,
  UserModel,
} from ":modules/users/repository/adapters/mongodb/user.model";

@singleton()
export class MongodbUsersRepository
  extends EntityRepository<IUser>
  implements IUsersRepository
{
  constructor() {
    super(UserModel);
  }

  async findUserByEmail(email: string): Promise<User | undefined> {
    const user = await this.findOne({
      filter: { email },
      options: { lean: true },
    });
    return user ? this._toEntity(user) : undefined;
  }

  async findUserById(id: string): Promise<User | undefined> {
    const user = await this.findOne({
      filter: { id },
      options: { lean: true },
    });
    return user ? this._toEntity(user) : undefined;
  }

  async findUsers(): Promise<User[]> {
    const users = await this.find({ filter: {}, options: { lean: true } });
    return users.map((user) => this._toEntity(user));
  }

  async createUser(user: User): Promise<User> {
    const document = this._toDocument(user);
    const newUser = await this.create({
      data: document,
      options: { lean: true },
    });
    return this._toEntity(newUser);
  }

  private _toEntity(document: IUser): User {
    return new User({
      id: document.id,
      email: document.email,
      firstname: document.firstname,
      lastname: document.lastname,
      active: document.active,
      roles: document.roles,
    });
  }

  private _toDocument(entity: User): Partial<IUser> {
    return {
      id: entity.id,
      email: entity.email,
      firstname: entity.firstname,
      lastname: entity.lastname,
      active: entity.active,
      roles: entity.roles,
    };
  }
}
