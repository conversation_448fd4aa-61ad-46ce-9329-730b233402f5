import { singleton } from "tsyringe";
import { User } from ":modules/users/entities/users.entity";
import { IUsersRepository } from ":modules/users/repository/users.interface";

@singleton()
export class InMemoryUsersRepository implements IUsersRepository {
  private _data: User[] = [];

  findUserByEmail(email: string): Promise<User | undefined> {
    return Promise.resolve(this._data.find((user) => user.email === email));
  }

  findUserById(id: string): Promise<User | undefined> {
    return Promise.resolve(this._data.find((user) => user.id === id));
  }

  findUsers(): Promise<User[]> {
    return Promise.resolve(this._data);
  }

  createUser(user: User): Promise<User> {
    this._data.push(user);
    return Promise.resolve(user);
  }
}
