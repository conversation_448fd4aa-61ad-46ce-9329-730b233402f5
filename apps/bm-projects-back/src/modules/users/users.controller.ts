import { singleton } from 'tsyringe';
import { NextFunction, Request, Response } from 'express';
import { ApiR<PERSON>ult, RequestWithUser } from ':helpers/utils/utility-types';
import GetUserByIdUseCase from ':modules/users/use-cases/get-user-by-id.use-case';
import { UserDto } from ':helpers/dtos/users/users.dto';
import { GetUsersUseCase } from ':modules/users/use-cases/get-users.use-case';

@singleton()
export default class UsersController {
  constructor(
    private readonly _getUserByIdUseCase: GetUserByIdUseCase,
    private readonly _getUsersUseCase: GetUsersUseCase
  ) {}

  async handleGetLoggedInUser(
    req: RequestWithUser,
    res: Response<ApiResult<UserDto>>,
    next: NextFunction
  ) {
    try {
      const reqUser = req.user;
      const user = await this._getUserByIdUseCase.execute(reqUser.userId);
      res.send({ data: user });
    } catch (error) {
      next(error);
    }
  }

  async handleGetUsers(
    _req: Request,
    res: Response<ApiResult<UserDto[]>>,
    next: NextFunction
  ) {
    try {
      const users = await this._getUsersUseCase.execute();
      res.send({ data: users });
    } catch (error) {
      next(error);
    }
  }
}
