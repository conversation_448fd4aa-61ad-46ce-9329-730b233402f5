import { z } from 'zod';
import { EntityConstructor } from ':helpers/utils/utility-types';
import { UserDto } from ':helpers/dtos/users/users.dto';
import { Role } from ':shared/enums/role';

export type UserProps = EntityConstructor<User>;

export class User {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  active: boolean;
  roles: Role[] = [];

  constructor(props: UserProps) {
    this.id = props.id;
    this.email = props.email;
    this.firstname = props.firstname;
    this.lastname = props.lastname;
    this.active = props.active;
    this.roles = props.roles;
  }

  toDto(): UserDto {
    return {
      id: this.id,
      email: this.email,
      firstname: this.firstname,
      lastname: this.lastname,
      active: this.active,
      roles: this.roles,
    };
  }
}
