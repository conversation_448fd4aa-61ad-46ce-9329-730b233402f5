import { logger } from ':helpers/logger';
import { CustomErrorCode } from ':helpers/errors/custom-error';

export const errorHandlerMiddleware = function (
  err: any,
  _req: any,
  res: any,
  _next: any
) {
  logger.error('[ERROR_HANDLER]', JSON.stringify(err));

  res
    .status(
      err.statusCode ??
        err.code ??
        err.status ??
        customErrorCodeToHttpStatus[err.customErrorCode as CustomErrorCode] ??
        500
    )
    .json({
      ...err,
      status: err.status,
      message: err.message,
      stack: err.stack,
    });
};

type CustomErrorType = `${CustomErrorCode}`;
const customErrorCodeToHttpStatus: Record<CustomErrorType, number> = {
  // UNAUTHORIZED
  UNAUTHORIZED_EMAIL_DOMAIN: 401,
  USER_NOT_ACTIVE: 401,
  INVALID_TOKEN: 401,

  // NOT FOUND
  USER_NOT_FOUND: 404,
};
