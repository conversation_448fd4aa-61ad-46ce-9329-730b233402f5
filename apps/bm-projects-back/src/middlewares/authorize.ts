import { container } from 'tsyringe';
import { Role } from ':shared/enums/role';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from ':helpers/services/jwt/jwt.service';
import { RequestWithUser } from ':helpers/utils/utility-types';

const _jwtService = container.resolve(JwtService);

export const authorize = function (
  roles: Role[] = []
): ((req: any, res: any, next: any) => void)[] {
  let authorizedRoles: Role[];
  if (roles.length === 0) {
    authorizedRoles = Object.values(Role);
  } else {
    authorizedRoles = [...roles];
  }

  return [
    // authenticate JWT token and attach user to request object (req.user)
    (req: Request, res: Response, next: NextFunction) => {
      const accessToken = req.headers.authorization;
      const accessTokenWithoutBearer = accessToken?.replace('Bearer ', '');

      if (!accessTokenWithoutBearer) {
        // TODO throw error instead (will be caught by error handler)
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const payload = _jwtService.verify(accessTokenWithoutBearer);
      if (!payload) {
        // TODO throw error instead (will be caught by error handler)
        return res.status(401).json({ message: 'Unauthorized' });
      }

      (req as RequestWithUser)['user'] = {
        userId: payload.userId,
        roles: payload.roles,
      };
      next();
    },

    // authorize based on user role
    (req: RequestWithUser, res: Response, next: NextFunction) => {
      if (!req.user.roles.some((role) => authorizedRoles.includes(role))) {
        // user's role is not authorized
        // TODO throw error instead (will be caught by error handler)
        return res
          .status(403)
          .json({ message: 'Unauthorized: role was not found in user roles' });
      }

      // authentication and authorization successful
      next();
    },
  ];
};
