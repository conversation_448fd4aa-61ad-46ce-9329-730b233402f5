import { z } from 'zod';

export const configValidator = z
  .object({
    db: z.object({
      type: z.enum(['mongodb', 'in-memory']),
    }),
    jwt: z.object({
      secretKey: z.string(),
    }),
    logger: z.object({
      level: z.string(),
    }),
    oauth: z.object({
      clientId: z.string(),
      clientSecret: z.string(),
      redirectUri: z.string(),
    }),
    settings: z.object({
      apiRoute: z.object({
        v1: z.string(),
      }),
      webBaseUrl: z.object({
        v1: z.string(),
      }),
    }),
  })
  .strict();
