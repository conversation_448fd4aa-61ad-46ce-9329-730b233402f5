export class CustomError extends Error {
  customErrorCode: CustomErrorCode;
  metadata: Record<string, any>;

  constructor(
    customErrorCode: CustomErrorCode,
    data: { message: string; metadata: Record<string, any> }
  ) {
    super(data.message);
    this.customErrorCode = customErrorCode;
    this.metadata = data.metadata;
  }
}

export enum CustomErrorCode {
  // AUTH
  UNAUTHORIZED_EMAIL_DOMAIN = "UNAUTHORIZED_EMAIL_DOMAIN",
  USER_NOT_ACTIVE = "USER_NOT_ACTIVE",
  INVALID_TOKEN = "INVALID_TOKEN",

  // USERS
  USER_NOT_FOUND = "USER_NOT_FOUND",
}
