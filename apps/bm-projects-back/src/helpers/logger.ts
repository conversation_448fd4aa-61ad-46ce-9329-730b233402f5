import chalk from "chalk";
import winston from "winston";

import { Config } from ":config";

const logColors = {
  error: chalk.white.bold.bgRed,
  fatal: chalk.white.bold.bgRed,
  info: chalk.white.bold.bgGreenBright,
  warn: chalk.white.bold.bgYellow,
  debug: chalk.white.bold.bgBlue,
};

const logsToFilterOut = ["Get enabled feature toggles for provided context"];

const customFormat = (
  { withColor = false }: { withColor: boolean } = { withColor: false }
) =>
  winston.format.printf(({ level, message, ...metadata }) => {
    const levelColored = withColor
      ? getColoredLevel(level)
      : `[${level.toUpperCase()}]`;
    return `${levelColored} ${message} ${stringifyMetadata(metadata)}`;
  });

const stringifyMetadata = (metadata: Record<string, any>): string => {
  try {
    if (!Object.keys(metadata).length) {
      return "";
    }
    if (Object.keys(metadata).length === 1 && metadata[0]) {
      return JSON.stringify(metadata[0]);
    }
    if (metadata?.[0]?.message) {
      return metadata[0].message;
    }
    return JSON.stringify(metadata);
  } catch (error) {
    return "could not parse error";
  }
};

const getColoredLevel = (level: string): string => {
  const levelValue = `[${level.toUpperCase()}]`;
  const levelIsKeyofLogColors = isValidLevel(level);
  return levelIsKeyofLogColors && logColors[level]
    ? logColors[level](levelValue)
    : levelValue;
};

const isValidLevel = (level: string): level is keyof typeof logColors => {
  return Object.keys(logColors).includes(level);
};

const winstonLogger = winston.createLogger({
  level: Config.logger.level,
  format: customFormat({ withColor: true }),
  transports: [new winston.transports.Console()],
});

class Logger {
  private logger: winston.Logger;

  constructor(logger: winston.Logger) {
    this.logger = logger;
  }

  debug(message: string, ...meta: any[]): void {
    if (!this._shouldLog(message)) {
      return;
    }
    this.logger.debug(message, this._getLoggerMeta(meta));
  }

  info(message: string, ...meta: any[]): void {
    if (!this._shouldLog(message)) {
      return;
    }
    this.logger.info(message, this._getLoggerMeta(meta));
  }

  warn(message: string, ...meta: any[]): void {
    if (!this._shouldLog(message)) {
      return;
    }
    this.logger.warn(message, this._getLoggerMeta(meta));
  }

  error(message: string, ...meta: any[]): void {
    if (!this._shouldLog(message)) {
      return;
    }
    this.logger.error(message, this._getLoggerMeta(meta));
  }

  fatal(message: string, ...meta: any[]): void {
    this.logger.error(message, this._getLoggerMeta(meta));
  }

  private _shouldLog = (message: string): boolean =>
    !logsToFilterOut.some((log) => message.toString().includes(log)) &&
    !["local-tests", "test"].includes(process.env.NODE_ENV ?? "");

  private _getLoggerMeta(meta: any[]): any[] {
    return {
      ...(meta ?? []),
    };
  }
}

export const logger = new Logger(winstonLogger);
