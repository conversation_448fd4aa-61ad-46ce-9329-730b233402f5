import { z } from 'zod';

enum ValidationErrors {
  BODY_VALIDATION_ERROR = 'body_validation_error',
  PARAMS_VALIDATION_ERROR = 'params_validation_error',
  QUERY_VALIDATION_ERROR = 'query_validation_error',
}

export function Body(schema: z.ZodSchema<any>) {
  return function (
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const [req, res] = args;
      const validationResult = schema.safeParse(req.body);

      if (!validationResult.success) {
        return res.status(422).json({
          success: false,
          message: ValidationErrors.BODY_VALIDATION_ERROR,
          errors: (validationResult as any).error.issues,
        });
      }

      req.body = validationResult.data;

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

export function Params(schema: z.ZodSchema<any>) {
  return function (
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const [req, res] = args;
      const validationResult = schema.safeParse(req.params);

      if (!validationResult.success) {
        return res.status(422).json({
          success: false,
          message: ValidationErrors.PARAMS_VALIDATION_ERROR,
          errors: (validationResult as any).error.issues,
        });
      }

      req.params = validationResult.data;

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

export function Query(schema: z.ZodSchema<any>) {
  return function (
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    descriptor.value = function (...args: any[]) {
      const [req, res] = args;
      const validationResult = schema.safeParse(req.query);

      if (!validationResult.success) {
        return res.status(422).json({
          success: false,
          message: ValidationErrors.QUERY_VALIDATION_ERROR,
          errors: (validationResult as any).error.issues,
        });
      }

      req.query = validationResult.data;

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}
