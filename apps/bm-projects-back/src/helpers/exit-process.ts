import { container } from "tsyringe";
import { logger } from "./logger";
import { DB } from "../db/db.port";

export const exitProcess = async (exitCode: 0 | 1): Promise<void> => {
  const db = container.resolve(DB);
  return Promise.all([db.end()])
    .then(() => logger.info("Services stopped successfully"))
    .catch((err) => logger.error("Error stopping services", err))
    .finally(() => process.exit(exitCode));
};
