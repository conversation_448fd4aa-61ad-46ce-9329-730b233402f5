import { Request } from "express";
import { Role } from ":shared/enums/role";

export type RemoveMethodsFromClass<T> = {
  [K in keyof T as T[K] extends Function ? never : K]: T[K];
};

export interface Timestampeable {
  createdAt: Date;
  updatedAt: Date;
}

export type EntityConstructor<T> = Omit<
  RemoveMethodsFromClass<T>,
  keyof Timestampeable
> &
  Partial<Timestampeable>;

// TODO merge with the one in the frontend
export type ApiResult<T, U = never> = { data: T } & ([U] extends [never]
  ? {}
  : { metadata?: U });

type extractGeneric<Type> = Type extends Request<infer X> ? X : never;
type extractGeneric2<Type> = Type extends Request<any, infer X> ? X : never;
type extractGeneric3<Type> = Type extends Request<any, any, infer X>
  ? X
  : never;
type extractGeneric4<Type> = Type extends Request<any, any, any, infer X>
  ? X
  : never;
type extractGeneric5<Type> = Type extends Request<any, any, any, any, infer X>
  ? X
  : never;

export type ReqUser = {
  userId: string;
  roles: Role[];
};

export interface RequestWithUser<
  P = extractGeneric<Request>,
  ResBody = extractGeneric2<Request>,
  ReqBody = extractGeneric3<Request>,
  ReqQuery = extractGeneric4<Request>,
  Locals extends Record<string, any> = extractGeneric5<Request>
> extends Request<P, ResBody, ReqBody, ReqQuery, Locals> {
  user: ReqUser;
}
