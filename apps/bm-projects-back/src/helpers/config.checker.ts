import { SafeParseError } from "zod";

import { Config } from ":config";

import { logger } from "./logger";
import { configValidator } from "./validators/config.validator";

export function checkConfigAndLogValidationErrors() {
  const safeParseResult = configValidator.safeParse(Config);
  if (safeParseResult.success) {
    logger.info("[CONFIG_CHECKER] Config validated successfully");
  } else {
    logger.warn(
      "[CONFIG_CHECKER] Config validation failed",
      (safeParseResult as SafeParseError<any>).error.errors
    );
  }
}
