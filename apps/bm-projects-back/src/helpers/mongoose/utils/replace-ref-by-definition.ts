export function replaceJSONSchemaRefsByDefinitions(jsonSchema: any) {
    // Fonction récursive pour parcourir le schéma
    function replaceRefs(schema: any) {
        // Vérifier si le schéma est un objet
        if (typeof schema === 'object' && schema !== null) {
            // Parcourir les clés du schéma
            for (const key in schema) {
                // Si la clé est "$ref" et que sa valeur est une référence
                if (key === '$ref' && typeof schema[key] === 'string') {
                    // Extraire le chemin de la référence
                    const refPath = schema[key].split('/');
                    // Trouver la définition correspondante dans le schéma
                    let refDefinition = jsonSchema;
                    for (let i = 1; i < refPath.length; i++) {
                        refDefinition = refDefinition[refPath[i]];
                    }
                    // Remplacer la référence par la définition
                    delete schema['$ref'];
                    schema = {
                        ...schema,
                        ...refDefinition,
                    };
                } else {
                    // Si la valeur de la clé est un objet, appeler récursivement replaceRefs
                    if (typeof schema[key] === 'object' && schema[key] !== null) {
                        schema[key] = replaceRefs(schema[key]);
                    }
                }
            }
        }
        return schema;
    }
    // Appeler la fonction récursive avec le schéma initial
    return replaceRefs(jsonSchema);
}
