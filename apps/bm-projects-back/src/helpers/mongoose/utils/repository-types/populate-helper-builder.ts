import { ProjectionFields } from 'mongoose';

import { VirtualsGetter } from ':helpers/mongoose/utils/virtuals-getter';

import { AppPopulateOption } from './app-populate-option';
import { FindOneResult } from './find-one-result';

export type PopulateBuilderHelper<
  Entity,
  Populate extends AppPopulateOption<VirtualsGetter<Entity>> | undefined,
  Projection extends ProjectionFields<Entity> = ProjectionFields<Entity>
> = FindOneResult<
  Entity,
  VirtualsGetter<Entity>,
  Projection,
  {
    lean: true;
    populate: Populate;
  }
>;
