export type ExecUpdateReturnType<T = any> = {
    lastErrorObject: {
        n: number;
        updatedExisting: boolean;
    };
    value: T;
    ok: number;
    $clusterTime: {
        clusterTime: {
            low: number;
            high: number;
            unsigned: boolean;
        };
        signature: {
            hash: {
                sub_type: number;
                buffer: Uint8Array;
                position: number;
            };
            keyId: {
                low: number;
                high: number;
                unsigned: boolean;
            };
        };
    };
    operationTime: {
        low: number;
        high: number;
        unsigned: boolean;
    };
};

export function isExecUpdateReturnType<T>(value: any): value is ExecUpdateReturnType<T> {
    return (
        typeof value === 'object' &&
        value !== null &&
        'lastErrorObject' in value &&
        'value' in value &&
        'ok' in value &&
        '$clusterTime' in value &&
        'operationTime' in value
    );
}
