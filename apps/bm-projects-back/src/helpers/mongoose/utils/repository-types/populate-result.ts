import { Overwrite } from 'utility-types';

import { VirtualsGetter } from ':helpers/mongoose/utils/virtuals-getter';

import { AppPopulateOption } from './app-populate-option';

export type OverwriteOrAssign<Entity, Virtual> = Virtual extends object
  ? Overwrite<
      Entity & {
        [K in Exclude<keyof Virtual, keyof Entity>]: Virtual[K];
      },
      Virtual
    >
  : Entity;

export type PopulateResult<
  IModel,
  Virtual,
  Populate extends AppPopulateOption<Virtual> | undefined
> = Populate extends AppPopulateOption<Virtual>
  ? OverwriteOrAssign<
      IModel,
      {
        [Opt in Populate[number] as Opt['path']]: Opt['path'] extends keyof Virtual
          ? Virtual[Opt['path']] extends Array<infer TypeOfTheArray>
            ? Opt['populate'] extends Array<any>
              ? OverwriteOrAssign<
                  TypeOfTheArray,
                  PopulateResult<
                    TypeOfTheArray,
                    VirtualsGetter<TypeOfTheArray>,
                    Opt['populate']
                  >
                >[]
              : TypeOfTheArray[]
            : Opt['populate'] extends AppPopulateOption<
                VirtualsGetter<Virtual[Opt['path']]>
              >
            ? OverwriteOrAssign<
                Virtual[Opt['path']],
                PopulateResult<
                  Virtual[Opt['path']],
                  VirtualsGetter<Virtual[Opt['path']]>,
                  Opt['populate']
                >
              >
            : Virtual[Opt['path']]
          : never;
      }
    >
  : IModel;
