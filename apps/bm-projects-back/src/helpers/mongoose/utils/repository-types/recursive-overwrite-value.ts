import mongoose from 'mongoose';

import {
  DoesExtend,
  If,
  IsPrimitive,
  RemoveNullOrUndefined,
} from ':helpers/utils/type-utils';

export type RecursiveOverwriteValue<T> = {
  [K in keyof T]-?: T[K] extends Array<infer N> | undefined | null
    ? If<
        DoesExtend<N, mongoose.Types.ObjectId>,
        string[],
        If<
          IsPrimitive<N>,
          RemoveNullOrUndefined<N>[],
          RecursiveOverwriteValue<RemoveNullOrUndefined<N>>[]
        >
      >
    : If<
        DoesExtend<T[K], mongoose.Types.ObjectId | undefined | null>,
        string,
        If<
          IsPrimitive<T[K]>,
          RemoveNullOrUndefined<T[K]>,
          RecursiveOverwriteValue<RemoveNullOrUndefined<T[K]>>
        >
      >;
};
