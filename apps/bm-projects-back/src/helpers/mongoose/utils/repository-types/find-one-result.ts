import { HydratedDocument, ProjectionFields, Types } from "mongoose";

import { ProjectedFieldsGetter } from ":helpers/mongoose/utils/repository-types/projected-fields-getter";

import { AppPopulateOption } from "./app-populate-option";
import { PopulateResult } from "./populate-result";

export type FindOneResult<
  Entity,
  Virtual,
  Projection extends ProjectionFields<Entity>,
  Options extends {
    lean?: boolean;
    populate?: AppPopulateOption<Virtual> | undefined;
  }
> = Options["lean"] extends boolean
  ? keyof Projection extends keyof PopulateResult<
      Entity,
      Virtual,
      Options["populate"]
    >
    ? Pick<
        PopulateResult<Entity, Virtual, Options["populate"]>,
        keyof Projection
      > & { _id: Types.ObjectId } & ProjectedFieldsGetter<Entity, Projection>
    : PopulateResult<Entity, Virtual, Options["populate"]>
  : keyof Projection extends keyof PopulateResult<
      Entity,
      Virtual,
      Options["populate"]
    >
  ? HydratedDocument<
      Pick<
        PopulateResult<Entity, Virtual, Options["populate"]>,
        keyof Projection
      > & {
        _id: Types.ObjectId;
      } & ProjectedFieldsGetter<Entity, Projection>
    >
  : HydratedDocument<PopulateResult<Entity, Virtual, Options["populate"]>>;
