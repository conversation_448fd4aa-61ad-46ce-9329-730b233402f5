import { VirtualsGetter } from ':helpers/mongoose/utils/virtuals-getter';

export type AppPopulateOption<Virtual> = RestrictedPopulateOptions<
  Virtual,
  keyof Virtual
>[];

export type RestrictedPopulateOptions<
  Virtual,
  Paths extends keyof Virtual
> = Paths extends infer Path
  ? {
      path: Paths;
      populate?: Path extends keyof Virtual
        ? Virtual[Path] extends Array<infer TypeOfTheArray>
          ? RestrictedPopulateOptions<
              VirtualsGetter<TypeOfTheArray>,
              keyof VirtualsGetter<TypeOfTheArray>
            >[]
          : RestrictedPopulateOptions<
              VirtualsGetter<Virtual[Path]>,
              keyof VirtualsGetter<Virtual[Path]>
            >[]
        : never;
      options?: {
        lean?: boolean;
      };
    }
  : never;
