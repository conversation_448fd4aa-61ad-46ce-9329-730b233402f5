import type { AnyBulkWriteOperation, BulkWriteResult } from 'mongodb';
import {
  Aggregate,
  AggregateOptions,
  ClientSession,
  Document,
  FilterQuery,
  Model,
  PipelineStage,
  ProjectionFields,
  QueryOptions,
  UpdateQuery,
  UpdateWithAggregationPipeline,
  UpdateWriteOpResult,
} from 'mongoose';
import { Object, String } from 'ts-toolbelt';

import { VirtualsGetter } from ':helpers/mongoose/utils/virtuals-getter';

import { AppPopulateOption } from ':helpers/mongoose/utils/repository-types/app-populate-option';
import { DeepPaths } from ':helpers/mongoose/utils/repository-types/deep-paths';
import { FindOneResult } from ':helpers/mongoose/utils/repository-types/find-one-result';
import { SortOptions } from ':helpers/mongoose/utils/repository-types/query-options';
import { RecursiveOverwriteValue } from ':helpers/mongoose/utils/repository-types/recursive-overwrite-value';
import {
  ExecUpdateReturnType,
  isExecUpdateReturnType,
} from ':helpers/mongoose/utils/repository-types/update-one';

export type { OverwriteOrAssign } from ':helpers/mongoose/utils/repository-types/populate-result';

export enum ReadPreferenceMode {
  PRIMARY = 'primary',
  PRIMARY_PREFERRED = 'primaryPreferred',
  SECONDARY = 'secondary',
  SECONDARY_PREFERRED = 'secondaryPreferred',
  NEAREST = 'nearest',
}

export class EntityRepository<Entity> {
  private readonly config: { returnDocumentAfterUpdate: boolean };
  readonly model: Model<Entity>;

  constructor(
    model: Model<Entity>,
    config: { returnDocumentAfterUpdate: boolean } = {
      returnDocumentAfterUpdate: true,
    }
  ) {
    this.model = model;
    this.config = config;
  }

  async create<
    Options extends {
      lean?: boolean;
    } = {}
  >({
    data,
    options,
  }: {
    data: Partial<Entity>;
    options?: Options;
  }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
    const doc = await this.model.create(data);

    if (options?.lean) {
      return doc.toObject() as any; // TODO improve typing
    }

    return doc as any; // TODO improve typing
  }

  async createMany<
    Options extends {
      ordered?: boolean;
      lean?: boolean;
      session?: ClientSession;
    } = {}
  >({
    data,
    options,
  }: {
    data: Partial<Entity>[];
    options?: Options;
  }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>[]> {
    const docs = await this.model.insertMany(data, {
      ...(options?.ordered && { ordered: options?.ordered }),
      ...(options?.session && { session: options?.session }),
    });

    if (options?.lean) {
      return docs.map((doc) => doc.toObject()) as any; // TODO improve typing
    }

    return docs as any;
  }

  aggregate<AggregateResultType = any>(
    pipeline: PipelineStage[],
    options?: AggregateOptions
  ): Aggregate<AggregateResultType[]> {
    return this.model.aggregate(pipeline, options);
  }

  // TODO: Change type d'operations en un type plus précis
  bulkOperations<T extends Document>({
    operations,
    options,
  }: {
    operations: AnyBulkWriteOperation<
      T extends Document<any, any, any> ? any : T extends {} ? T : any
    >[];
    options?: { ordered?: boolean };
  }): Promise<BulkWriteResult> {
    return this.model.bulkWrite(operations as any, options) as any; // TODO improve typing
  }

  findOne<
    Projection extends ProjectionFields<Entity>,
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      sort?: SortOptions<Entity>;
      readPreference?: ReadPreferenceMode;
    } = {}
  >({
    filter,
    projection,
    options,
  }: {
    filter: FilterQuery<Entity>;
    projection?: Projection | null;
    options?: Options;
  }): Promise<FindOneResult<
    Entity,
    VirtualsGetter<Entity>,
    Projection,
    Options
  > | null> {
    return this.model
      .findOne(filter, projection, options as QueryOptions<Entity>)
      .exec() as any;
  }

  async findOneOrFail<
    Projection extends ProjectionFields<Entity>,
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      sort?: SortOptions<Entity>;
      readPreference?: ReadPreferenceMode;
    } = {}
  >({
    filter,
    projection,
    options,
  }: {
    filter: FilterQuery<Entity>;
    projection?: Projection | null;
    options?: Options;
  }): Promise<
    FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options>
  > {
    const result = (await this.model
      .findOne(filter, projection, options as QueryOptions<Entity>)
      .exec()) as any;
    if (!result) {
      throw new Error(`${this.model.modelName} not found`);
    }
    return result;
  }

  find<
    Projection extends ProjectionFields<Entity>,
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      sort?: SortOptions<Entity>;
      readPreference?: ReadPreferenceMode;
    } = {}
  >({
    filter,
    projection,
    options,
  }: {
    filter: FilterQuery<Entity>;
    projection?: Projection | null;
    options?: Options;
  }): Promise<
    FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options>[]
  > {
    return this.model
      .find(filter, projection, options as QueryOptions<Entity>)
      .exec() as any;
  }

  findOneAndUpdate<
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      new?: boolean;
      session?: ClientSession;
    } = {}
  >({
    filter,
    update,
    options,
  }: {
    filter: FilterQuery<Entity>;
    update: UpdateQuery<Entity> | UpdateWithAggregationPipeline | undefined;
    options?: Options;
  }): Promise<FindOneResult<
    Entity,
    VirtualsGetter<Entity>,
    never,
    Options
  > | null> {
    if ((options as any)?.upsert) {
      throw new Error('upsert option is banned, please use upsert method');
    }

    return this.model
      .findOneAndUpdate(filter, update, {
        new: options?.new ?? this.config.returnDocumentAfterUpdate,
        ...options,
      } as any)
      .exec() as any;
  }

  findOneAndUpdateOrFail<
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      new?: boolean;
      session?: ClientSession;
    } = {}
  >({
    filter,
    update,
    options,
  }: {
    filter: FilterQuery<Entity>;
    update: UpdateQuery<Entity> | UpdateWithAggregationPipeline | undefined;
    options?: Options;
  }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
    if ((options as any)?.upsert) {
      throw new Error('upsert option is banned, please use upsert method');
    }

    const result = this.model
      .findOneAndUpdate(filter, update, {
        new: options?.new ?? this.config.returnDocumentAfterUpdate,
        ...options,
      } as any)
      .exec() as any;

    if (!result) {
      throw new Error(`${this.model.modelName} not found for update`);
    }
    return result;
  }

  async upsert<
    Options extends {
      lean?: boolean;
      populate?: AppPopulateOption<VirtualsGetter<Entity>>;
      new?: boolean;
      session?: ClientSession;
    } = {}
  >({
    filter,
    update,
    options,
  }: {
    filter: FilterQuery<Entity>;
    update: Partial<Entity>;
    options?: Options;
  }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
    if ((options as QueryOptions<Entity>)?.upsert) {
      throw new Error('upsert option is banned, do not try to use it!');
    }
    const target = (await this.model
      .findOneAndUpdate(filter, update, {
        new: options?.new ?? this.config.returnDocumentAfterUpdate,
        ...options,
      } as QueryOptions<Entity>)
      .exec()) as Options['lean'] extends true
      ? FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>
      : ExecUpdateReturnType<
          FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>
        >;

    // If document is found and updated, return it
    if (
      isExecUpdateReturnType<
        FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>
      >(target) &&
      target.value
    ) {
      return target.value;
    } else if (target) {
      return target as FindOneResult<
        Entity,
        VirtualsGetter<Entity>,
        never,
        Options
      >;
    }

    // Note that you **must**
    // pass an array as the first parameter to `create()` if you want to
    // specify options.
    // https://mongoosejs.com/docs/api/model.html#Model.create()
    if (options?.session) {
      const doc = await this.model.create(
        [
          {
            ...filter,
            ...update,
          },
        ],
        { session: options?.session }
      );
      return this.findOne({ filter: { _id: doc[0]._id }, options }) as any;
    }

    // Else, create it
    const doc = await this.model.create({
      ...filter,
      ...update,
    });
    return this.findOne({ filter: { _id: doc._id }, options }) as any;
  }

  updateMany({
    filter,
    update,
    options,
  }: {
    filter: FilterQuery<Entity>;
    update: UpdateQuery<Entity> | UpdateWithAggregationPipeline | undefined;
    options?: Omit<QueryOptions<Entity>, 'upsert'> | null;
  }): Promise<UpdateWriteOpResult> {
    if (options?.upsert) {
      throw new Error('upsert option is banned, please use upsert method');
    }

    return this.model.updateMany(filter, update, options).exec();
  }

  countDocuments({ filter }: { filter: FilterQuery<Entity> }): Promise<number> {
    return this.model.countDocuments(filter).exec();
  }

  distinct<Field extends DeepPaths<RecursiveOverwriteValue<Entity>>>(
    field: Field,
    filter?: FilterQuery<Entity>
  ): Promise<Object.Path<Entity, String.Split<Field, '.'>>[]> {
    return this.model.distinct(field, filter).exec() as any;
  }

  deleteOne({
    filter,
    options,
  }: {
    filter: FilterQuery<Entity>;
    options?: { session?: ClientSession };
  }): Promise<{ acknowledged: boolean; deletedCount: number }> {
    return this.model.deleteOne(filter, options).exec();
  }

  deleteMany({
    filter,
    options,
  }: {
    filter: FilterQuery<Entity>;
    options?: { session?: ClientSession };
  }): Promise<{ acknowledged: boolean; deletedCount: number }> {
    return this.model.deleteMany(filter, options).exec();
  }
}
