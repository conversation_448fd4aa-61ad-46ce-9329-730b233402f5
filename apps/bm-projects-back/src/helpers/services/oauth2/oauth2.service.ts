import axios from 'axios';
import { Config } from ':config';
import {
  getOAuth2TokenResponseValidator,
  getOAuth2UserProfileResponseValidator,
} from './oauth2.validators';
import { OAuth2UserProfile } from './oauth2.interface';

export class OAuth2Service {
  private readonly CLIENT_ID = Config.oauth.clientId;
  private readonly CLIENT_SECRET = Config.oauth.clientSecret;
  private readonly REDIRECT_URI = Config.oauth.redirectUri;

  async getOAuth2Token(code: string): Promise<string> {
    const response = await axios.post('https://oauth2.googleapis.com/token', {
      client_id: this.CLIENT_ID,
      client_secret: this.CLIENT_SECRET,
      code,
      redirect_uri: this.REDIRECT_URI,
      grant_type: 'authorization_code',
    });

    return getOAuth2TokenResponseValidator.parse(response).data.access_token;
  }

  async getUserProfile(accessToken: string): Promise<OAuth2UserProfile> {
    const response = await axios.get(
      'https://www.googleapis.com/oauth2/v1/userinfo',
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      }
    );

    return getOAuth2UserProfileResponseValidator.parse(response).data;
  }
}
