import { z } from 'zod';
import { Role } from ':shared/enums/role';
import ms from 'ms';

export const jwtContentValidator = z.object({
  userId: z.string(),
  roles: z.array(z.nativeEnum(Role)),
});

export type JwtContent = z.infer<typeof jwtContentValidator>;

export interface JwtPayload {
  [key: string]: any;
  iss?: string | undefined;
  sub?: string | undefined;
  aud?: string | string[] | undefined;
  exp?: number | undefined;
  nbf?: number | undefined;
  iat?: number | undefined;
  jti?: string | undefined;
}

export interface SignOptions {
  expiresIn: ms.StringValue;
}

export interface IJwtPort {
  verify(accessToken: string, secretKey: string): JwtPayload | string;
  sign(payload: any, secretKey: string, options?: SignOptions): string;
}
