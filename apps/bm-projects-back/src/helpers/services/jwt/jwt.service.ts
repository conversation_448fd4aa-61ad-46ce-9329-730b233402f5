import { inject, singleton } from "tsyringe";
import { Config } from ":config";
import {
  IJwtPort,
  JwtContent,
  jwtContentValidator,
  SignOptions,
} from "./jwt.interface";
import { JsonwebtokenAdapter } from "./adapters/jsonwebtoken.adapter";

@singleton()
export class JwtService {
  private readonly _secretKey = Config.jwt.secretKey;
  private readonly _expiresIn = "1h";

  constructor(
    @inject(JsonwebtokenAdapter) private readonly _jwtPort: IJwtPort
  ) {}

  verify(accessToken: string): JwtContent | null {
    try {
      const decoded = this._jwtPort.verify(accessToken, this._secretKey);
      const validation = jwtContentValidator.safeParse(decoded);
      return validation.success ? validation.data : null;
    } catch (error) {
      return null;
    }
  }

  generate(payload: any): string {
    const options: SignOptions = {
      expiresIn: this._expiresIn,
    };
    return this._jwtPort.sign(payload, this._secretKey, options);
  }
}
