import { singleton } from 'tsyringe';
import {
  IJwtPort,
  JwtPayload,
  SignOptions,
} from ':helpers/services/jwt/jwt.interface';
import { sign, verify, SignOptions as JWTSignOptions } from 'jsonwebtoken';

@singleton()
export class JsonwebtokenAdapter implements IJwtPort {
  verify(accessToken: string, secretKey: string): JwtPayload | string {
    return verify(accessToken, secretKey);
  }

  sign(payload: any, secretKey: string, options?: SignOptions): string {
    const signOptions: JWTSignOptions = {
      ...(options ?? {}),
    };
    return sign(payload, secretKey, signOptions);
  }
}
