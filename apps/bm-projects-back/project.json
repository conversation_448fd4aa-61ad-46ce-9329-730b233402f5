{"name": "bm-projects-back", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/bm-projects-back/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "bm-projects-back:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "bm-projects-back:build:development"}, "production": {"buildTarget": "bm-projects-back:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}