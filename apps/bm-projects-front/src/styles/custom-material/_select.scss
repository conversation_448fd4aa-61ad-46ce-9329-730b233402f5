.custom-form-field {
  --mdc-filled-text-field-container-color: "#F9FAFF";
  --mdc-filled-text-field-focus-active-indicator-color: "#F9FAFF";

  .mat-mdc-form-field-infix {
      min-height: 48px;
      --mat-form-field-container-vertical-padding: 11px;
  }

  .mat-mdc-select-disabled {
      opacity: 0.5;
      --mat-select-disabled-trigger-text-color: var(--color-custom-text);
  }

  &.mat-focused .mat-mdc-select-arrow {
      transform: rotate(180deg);
  }
}

.custom-filled-form-field {
  @extend .custom-form-field;

  --mdc-filled-text-field-container-color: var(--color-custom-white);
  --mat-select-trigger-text-size: 12px;
  --mat-select-trigger-text-weight: 500;
  --mat-select-enabled-trigger-text-color: var(--color-custom-text);

  .mdc-text-field--filled.mdc-text-field--disabled {
      --mdc-filled-text-field-disabled-container-color: var(--color-custom-white);
  }

  .mdc-text-field--filled {
      border-radius: 100px;
      padding: 0 20px;
      border: 1px solid "#F2F2FF";
      --mdc-filled-text-field-active-indicator-height: 0;

  }

  &.mat-mdc-form-field:hover .mat-mdc-form-field-focus-overlay,
  &.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay {
      opacity: 0;
  }
}

.mat-form-field-disabled, .mat-form-field-disabled input {
  cursor: not-allowed;
}

.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::-webkit-input-placeholder,
  .mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
      --mdc-filled-text-field-input-text-placeholder-color: "#a5afb9";
      --mat-form-field-disabled-input-text-placeholder-color: '#778696';

      font-style: italic;
  }
