@use '_mixins' as *;
@use '../tailwind-theme';

$transition-time: 0.15s;
$custom-card-padding: 15px;
$custom-card-footer-padding: 5px 15px;
$custom-card-margin-top: 15px;
$custom-card-margin-bottom: 15px;
$custom-card-margin-side: 10px;

.custom-card-container {
    margin: 0 auto;
    max-width: 900px;
}

.custom-card {
    display: flex;
    flex: 1 1 300px;
    flex-direction: column;
    position: relative;
    border-radius: 10px;
    padding: 26px;
    border: 1px solid #F2F2FF;
    background-color: var(--color-custom-primary) !important;

    &.custom-hover:hover {
        transition: all $transition-time;
        @include custom-shadow-up;

        .custom-card__header,
        .custom-content-hover {
            transition: all $transition-time;
        }
    }

    .custom-card__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        padding-bottom: 26px;
        color: "#192a3e";
        border-radius: 4px;

        &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            width: 100%;
            bottom: 0;
            margin: 0 auto;
        }
    }

    .custom-card__body--padding-2x {
        padding: 2 * $custom-card-padding;
    }

    .custom-card__image {
        align-self: center;
        max-width: 100%;
    }

    .custom-card__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        min-width: 32px;
    }

    .custom-card__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        padding: $custom-card-footer-padding;
        color: "#192a3e";

        &::before {
            border-bottom: 1px solid "#F2F2FF";
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            width: 100%;
            top: 0;
            margin: 0 auto;
        }
    }

    .custom-card__footer mat-icon {
        vertical-align: middle;
    }

    .custom-card__footer li {
        display: flex;
    }

    .custom-card__seperator {
        position: relative;
        border-bottom: 1px solid "#F2F2FF";
        left: -$custom-card-padding;
        right: -$custom-card-padding;
        width: calc(100% + 2 * #{$custom-card-padding});
        margin: $custom-card-margin-top 0 $custom-card-margin-bottom 0;
    }
}
