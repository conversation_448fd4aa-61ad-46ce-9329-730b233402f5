// Mixin to manage custom shadows
@mixin custom-shadow-up {
    box-shadow: 0px 14px 28px rgba($color: black, $alpha: 0.25);
}

// Mixin to manage disabled buttons
// TODO improve this, don't need mixin
@mixin disabled($textColor) {
  &:disabled {
      color: $textColor;
      --mdc-protected-button-disabled-label-text-color: #{$textColor};
      --mdc-text-button-disabled-label-text-color: #{$textColor};
      --mdc-filled-button-disabled-label-text-color: #{$textColor};
      filter: opacity(0.5);

      &:hover::before {
          opacity: 0;
      }
  }
}

// Breakpoints
$custom-breakpoints: (
    small: 639px,
    medium: 767px,
    large: 1023px,
    extra-large: 1279px,
);

/// Mixin to manage responsive breakpoints
@mixin custom-respond-to($breakpoint) {
  // If the key exists in the map
  @if map-has-key($custom-breakpoints, $breakpoint) {
      // Prints a media query based on the value
      @media (max-width: map-get($custom-breakpoints, $breakpoint)) {
          @content;
      }
  }

  // If the key doesn't exist in the map
  @else {
      @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
      + "Available breakpoints are: #{map-keys($custom-breakpoints)}.";
  }
}
