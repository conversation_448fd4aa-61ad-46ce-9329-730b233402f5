import {
  APP_INITIALIZER,
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { JwtModule } from '@auth0/angular-jwt';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

import { environment } from ':environments/environment';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { ApplicationLanguage } from ':shared/enums/application-language';
import { AuthGuard } from ':core/guards/auth.guard';
import { AdminGuard } from ':core/guards/admin.guard';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { commonRequestInterceptor } from ':core/interceptors/common-request.interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(
      withInterceptorsFromDi(),
      withInterceptors([commonRequestInterceptor])
    ),
    importProvidersFrom(
      JwtModule.forRoot({
        config: {
          tokenGetter: tokenGetter,
          allowedDomains: [environment.apiDomain],
        },
      }),
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: createTranslateLoader,
          deps: [HttpClient],
        },
        defaultLanguage:
          localStorage.getItem(LocalStorageKey.LANGUAGE) ??
          ApplicationLanguage.FR,
      })
    ),
    AuthGuard,
    AdminGuard,
    provideAnimationsAsync(),
    {
      provide: APP_INITIALIZER,
      useFactory: appTranslationInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
};
export function tokenGetter(): string | null {
  return (
    (typeof window !== 'undefined'
      ? localStorage.getItem(LocalStorageKey.JWT_TOKEN)
      : null) ?? null
  );
}

export function createTranslateLoader(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(
    http,
    '/i18n/',
    '.json?cacheBuster=' + new Date().getTime()
  );
}

export function appTranslationInitializerFactory(translate: TranslateService) {
  return (): Promise<any> =>
    new Promise<any>((resolve: any) => {
      const langToSet =
        localStorage.getItem(LocalStorageKey.LANGUAGE) ??
        ApplicationLanguage.FR;
      translate.setDefaultLang(langToSet);
      translate.use(langToSet).subscribe({
        next: () => {
          console.info(`Successfully initialized '${langToSet}' language.`);
        },
        error: (err) => {
          console.error(`Problem with '${langToSet}' language initialization.`);
          console.error(err);
        },
        complete: () => {
          resolve(null);
        },
      });
    });
}
