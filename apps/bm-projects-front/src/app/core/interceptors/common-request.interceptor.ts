import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';

export function commonRequestInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
  const reqWithCredentials = req.clone({
    withCredentials: true,
  });
  console.log('commonRequestInterceptor', reqWithCredentials.url);
  return next(reqWithCredentials);
}
