import { AuthService } from ':core/services/auth.service';
import { UsersService } from ':core/services/users.service';
import { Role } from ':shared/enums/role';
import { IUser } from ':shared/models/users.model';
import { computed, inject } from '@angular/core';
import { tapResponse } from '@ngrx/operators';
import {
  patchState,
  signalStore,
  withComputed,
  withMethods,
  withState,
} from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { firstValueFrom, pipe, switchMap, tap } from 'rxjs';

// STATE INTERFACE
type CurrentUserState = {
  user: IUser;
  loading: boolean;
  error: string;
};

// INITIAL STATE
const DEFAULT_USER: IUser = {
  id: '',
  email: '',
  firstname: '',
  lastname: '',
  active: false,
  roles: [],
};
const initialState: CurrentUserState = {
  user: DEFAULT_USER,
  loading: false,
  error: '',
};

// STORE DEFINITION
export const CurrentUserStore = signalStore(
  { providedIn: 'root' },

  // STATE
  withState(initialState),

  // METHODS
  withMethods(
    (
      store,
      usersService = inject(UsersService),
      authService = inject(AuthService)
    ) => ({
      // async method
      async fetchCurrentUser(): Promise<void> {
        patchState(store, { loading: true });

        try {
          const user = await firstValueFrom(usersService.getCurrentUser());
          patchState(store, { user, error: '' });
        } catch (error) {
          patchState(store, {
            user: DEFAULT_USER,
            error: JSON.stringify(error),
          });
          authService.logout();
        } finally {
          patchState(store, { loading: false });
        }
      },

      // rxjs method
      fetchCurrentUserV2: rxMethod<void>(
        pipe(
          tap(() => patchState(store, { loading: true })),

          switchMap(() => usersService.getCurrentUser()),

          tapResponse({
            next: (user) => patchState(store, { user, error: '' }),

            error: (error) => {
              patchState(store, {
                user: DEFAULT_USER,
                error: JSON.stringify(error),
              });
              authService.logout();
            },

            finalize: () => patchState(store, { loading: false }),
          })
        )
      ),
    })
  ),

  // COMPUTED VALUES
  withComputed(({ user }) => ({
    currentUserInitials: computed(() => {
      const currentUser = user();
      if (!currentUser) {
        return '';
      }
      return `${user.firstname()[0]}${currentUser.lastname[0]}`.toUpperCase();
    }),
    isAdmin: computed(() => {
      return user()?.roles.includes(Role.ADMIN);
    }),
  }))
);
