import { AuthService } from ':core/services/auth.service';
import { CurrentUserStore } from ':core/stores/current-user.store';
import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

@Injectable()
export class AdminGuard {
    private readonly _authService = inject(AuthService);
    private readonly _currentUserStore = inject(CurrentUserStore);

    async canActivate(_route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Promise<boolean> {
        if (!this._currentUserStore.isAdmin()) {
            this._authService.logout();
            return false;
        }
        return true;
    }
}
