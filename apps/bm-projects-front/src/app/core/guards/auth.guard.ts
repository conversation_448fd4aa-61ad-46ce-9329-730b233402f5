import { AuthService } from ':core/services/auth.service';
import { CurrentUserStore } from ':core/stores/current-user.store';
import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

@Injectable()
export class AuthGuard {
    private readonly _authService = inject(AuthService);
    private readonly _currentUserStore = inject(CurrentUserStore);
    private readonly _router = inject(Router);

    async canActivate(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {
        const isAuthenticated = await this._authService.isAuthenticated();
        if (!isAuthenticated) {
            this._authService.setRedirectUrl(state.url);
            this._router.navigate(['login']);
            return false;
        }

        await this._currentUserStore.fetchCurrentUser();

        const redirectUrl = this._authService.getRedirectUrl();
        this._authService.setRedirectUrl(null);
        if (redirectUrl) {
            this._router.navigate([redirectUrl]);
        }
        return true;
    }
}
