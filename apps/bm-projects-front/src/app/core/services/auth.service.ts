import { environment } from ':environments/environment';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { ApiResult } from ':shared/utils/utility-types';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { lastValueFrom, map, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    readonly API_SERVICE_BASE_URL = `${environment.apiBaseUrl}/auth`;

    private readonly _http = inject(HttpClient);
    private readonly _router = inject(Router);

    logout(): void {
        localStorage.removeItem(LocalStorageKey.JWT_TOKEN);
        this._router.navigate(['login']);
    }

    loginWithGoogle(): Observable<{ redirectUrl: string }> {
        return this._http
            .get<ApiResult<{ redirectUrl: string }>>(`${this.API_SERVICE_BASE_URL}/google/login`)
            .pipe(map(({ data }) => data));
    }

    async isAuthenticated(): Promise<boolean> {
        const jwtToken = localStorage?.getItem(LocalStorageKey.JWT_TOKEN);
        const isTokenValid = jwtToken && (await this._isTokenValid(jwtToken ?? ''));
        if (isTokenValid) {
            return true;
        }

        const jwtTokenFromCookies = this._extractJwtTokenFromCookies();
        const isTokenFromCookiesValid = jwtTokenFromCookies && (await this._isTokenValid(jwtTokenFromCookies));
        if (isTokenFromCookiesValid) {
            localStorage.setItem(LocalStorageKey.JWT_TOKEN, jwtTokenFromCookies);
            return true;
        }

        localStorage.removeItem(LocalStorageKey.JWT_TOKEN);
        return false;
    }

    setRedirectUrl(redirectUrl: string | null): void {
        localStorage.setItem(LocalStorageKey.REDIRECT_URL, redirectUrl ?? '');
    }

    getRedirectUrl(): string | null {
        return localStorage.getItem(LocalStorageKey.REDIRECT_URL) ?? null;
    }

    private _extractJwtTokenFromCookies(): string | null {
        const cookies = Object.fromEntries(document.cookie.split('; ').map((v) => v.split(/=(.*)/s).map(decodeURIComponent))) ?? {};

        return cookies['accessToken'] ?? null;
    }

    private async _isTokenValid(accessToken: string): Promise<boolean> {
        const request$ = this._http
            .post<ApiResult<boolean>>(`${this.API_SERVICE_BASE_URL}/is-token-valid`, {
                accessToken,
            })
            .pipe(map(({ data: isValid }) => isValid));

        return await lastValueFrom<boolean>(request$);
    }
}
