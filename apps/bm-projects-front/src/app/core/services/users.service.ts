import { environment } from ':environments/environment';
import { UserDto } from ':shared/dtos/users.dto';
import { User } from ':shared/models/users.model';
import { ApiResult } from ':shared/utils/utility-types';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class UsersService {
    readonly API_SERVICE_BASE_URL = `${environment.apiBaseUrl}/users`;

    private readonly _http = inject(HttpClient);

    getCurrentUser(): Observable<User> {
        return this._http.get<ApiResult<UserDto>>(`${this.API_SERVICE_BASE_URL}/me`).pipe(map(({ data }) => User.fromUserDto(data)));
    }

    getAllUsers(): Observable<User[]> {
        return this._http.get<ApiResult<UserDto[]>>(this.API_SERVICE_BASE_URL).pipe(map(({ data }) => data.map(User.fromUserDto)));
    }
}
