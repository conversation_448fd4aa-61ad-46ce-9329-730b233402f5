import { Routes } from '@angular/router';
import { AuthGuard } from ':core/guards/auth.guard';
import { LOGGED_IN_ROUTES } from ':shared/routes/logged-in.routes';
import { LOGGED_OUT_ROUTES } from ':shared/routes/logged-out.routes';

export const routes: Routes = [
  {
    path: '',
    loadChildren: () => LOGGED_IN_ROUTES,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: '',
    loadChildren: () => LOGGED_OUT_ROUTES,
  },
];
