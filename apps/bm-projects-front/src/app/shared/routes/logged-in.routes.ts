import { AdminGuard } from ':core/guards/admin.guard';
import { ADMIN_ROUTES } from ':modules/admin/admin.routes';
import { HomeComponent } from ':modules/home/<USER>';
import { LoggedInRootComponent } from ':shared/components/logged-in-root/logged-in-root.component';
import { Routes } from '@angular/router';
import { ComponentsDisplayComponent } from ':modules/components-display/components-display.component';

export const LOGGED_IN_ROUTES: Routes = [
  {
    path: '',
    component: LoggedInRootComponent,
    children: [
      {
        path: '',
        component: HomeComponent,
      },
      {
        path: 'components',
        component: ComponentsDisplayComponent,
      },
      {
        path: 'admin',
        loadChildren: () => ADMIN_ROUTES,
        canActivate: [AdminGuard],
        runGuardsAndResolvers: 'always',
      },
    ],
  },
];
