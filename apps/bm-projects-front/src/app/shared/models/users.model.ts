import { UserDto } from ':shared/dtos/users.dto';
import { Role } from ':shared/enums/role';

export interface IUser {
    id: string;
    email: string;
    firstname: string;
    lastname: string;
    active: boolean;
    roles: Role[];
}

export class User implements IUser {
    id: string;
    email: string;
    firstname: string;
    lastname: string;
    active: boolean;
    roles: Role[];

    constructor(user: IUser) {
        this.id = user.id;
        this.email = user.email;
        this.firstname = user.firstname;
        this.lastname = user.lastname;
        this.active = user.active;
        this.roles = user.roles;
    }

    toIUser(): IUser {
        return {
            id: this.id,
            email: this.email,
            firstname: this.firstname,
            lastname: this.lastname,
            active: this.active,
            roles: this.roles,
        };
    }

    static fromUserDto(userDto: UserDto): User {
        return new User({
            id: userDto.id,
            email: userDto.email,
            firstname: userDto.firstname,
            lastname: userDto.lastname,
            active: userDto.active,
            roles: userDto.roles,
        });
    }
}
