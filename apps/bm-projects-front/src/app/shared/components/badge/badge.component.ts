import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';

type BadgeSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type BadgeType =
  | 'primary'
  | 'secondary'
  | 'accent'
  | 'neutral'
  | 'info'
  | 'success'
  | 'warning'
  | 'error';
type BadgeCategory = 'soft' | 'outline' | 'dash' | 'ghost';

@Component({
  selector: 'app-badge',
  standalone: true,
  imports: [],
  templateUrl: './badge.component.html',
  styleUrls: ['./badge.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BadgeComponent {
  readonly text = input<string>('');
  readonly size = input<BadgeSize>('md');
  readonly type = input<BadgeType>('primary');
  readonly category = input<BadgeCategory | null>(null);

  readonly badgeTypeClass = computed(() => {
    switch (this.type()) {
      case 'primary':
        return 'badge-primary';
      case 'secondary':
        return 'badge-secondary';
      case 'accent':
        return 'badge-accent';
      case 'neutral':
        return 'badge-neutral';
      case 'info':
        return 'badge-info';
      case 'success':
        return 'badge-success';
      case 'warning':
        return 'badge-warning';
      case 'error':
        return 'badge-error';
      default:
        return 'badge-primary';
    }
  });

  readonly badgeCategoryClass = computed(() => {
    switch (this.category()) {
      case 'soft':
        return 'badge-soft';
      case 'outline':
        return 'badge-outline';
      case 'dash':
        return 'badge-dash';
      case 'ghost':
        return 'badge-ghost';
      default:
        return '';
    }
  });

  readonly badgeSizeClass = computed(() => {
    switch (this.size()) {
      case 'xs':
        return 'badge-xs';
      case 'sm':
        return 'badge-sm';
      case 'lg':
        return 'badge-lg';
      case 'xl':
        return 'badge-xl';
      default:
        return 'badge-md';
    }
  });
}
