import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

@Component({
  selector: 'app-avatar',
  standalone: true,
  templateUrl: './avatar.component.html',
  styleUrls: ['./avatar.component.scss'],
  imports: [NgClass],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvatarComponent {
  readonly imageUrl = input<string | null>(null);
  readonly initials = input<string>('');
  readonly size = input<'xs' | 'md' | 'xl' | '3xl'>('md');
}
