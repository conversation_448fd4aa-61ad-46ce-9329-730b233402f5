<!-- <mat-card class="flex flex-col gap-3 custom-card">
    <ng-content></ng-content>
</mat-card> -->

<div class="card shadow-sm">
  <div class="card-body">
    @if (title()) {
    <h2 class="card-title">{{ title() }}</h2>
    } @if (text()) {
    <p>{{ text() }}</p>
    } @if(actions(); as actions) { @if (actions.length > 0) {
    <div class="card-actions justify-end">
      @for (action of actions; track action) {
      <app-button
        [text]="action.text"
        (buttonClick)="action.click()"
      ></app-button>
      }
    </div>
    } }
    <ng-content></ng-content>
  </div>
</div>
