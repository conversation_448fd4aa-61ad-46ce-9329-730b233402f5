import { CurrentUserStore } from ':core/stores/current-user.store';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core';
import { Router } from '@angular/router';
import { AvatarComponent } from ':shared/components/avatar/avatar.component';

@Component({
  selector: 'app-global-header',
  templateUrl: './global-header.component.html',
  styleUrls: ['./global-header.component.scss'],
  standalone: true,
  imports: [ImagePathResolverPipe, AvatarComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GlobalHeaderComponent {
  private readonly _currentUserStore = inject(CurrentUserStore);
  private readonly _router = inject(Router);

  readonly currentUserInitials = computed(() =>
    this._currentUserStore.currentUserInitials()
  );

  goToHome = () => {
    this._router.navigate(['']);
  };
}
