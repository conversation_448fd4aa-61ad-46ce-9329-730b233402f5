<div>
    @if (title(); as title) {
        <div class="flex">
            <span>{{ title }}</span>
            @if (required(); as required) {
                <span>*</span>
            }
        </div>
    }
    @if (subtitle(); as subtitle) {
        <div>{{ subtitle }}</div>
    }
    <mat-form-field class="custom-filled-form-field" subscriptSizing="dynamic">
        <mat-select
            [id]="id()"
            [compareWith]="computedCompareWith()"
            [disabled]="disabled()"
            [multiple]="multiple()"
            [placeholder]="placeholder()"
            [required]="required()"
            [value]="value()"
            (selectionChange)="onSelectionChange($event)">
            @if (selectedValueBaseTemplate(); as selectedValueBaseTemplate) {
                <mat-select-trigger>
                    <ng-container
                        [ngTemplateOutlet]="selectedValueBaseTemplate"
                        [ngTemplateOutletContext]="
                            multiple() ? { selectedValues: selectedValues() } : { selectedValue: selectedValue() }
                        "></ng-container>
                </mat-select-trigger>
            }
            @for (option of options(); track option) {
                <mat-option [value]="option">
                    @if (optionBaseTemplate(); as optionBaseTemplate) {
                        <ng-container [ngTemplateOutlet]="optionBaseTemplate" [ngTemplateOutletContext]="{ option }"></ng-container>
                    } @else {
                        {{ computedDisplayWith() | exec: option }}
                    }
                </mat-option>
            }
        </mat-select>
    </mat-form-field>
</div>
