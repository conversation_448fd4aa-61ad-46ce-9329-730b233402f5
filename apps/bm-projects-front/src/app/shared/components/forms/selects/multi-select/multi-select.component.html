<app-base-select
    [id]="id()"
    [compareWith]="compareWith()"
    [displayWith]="displayWith()"
    [disabled]="disabled()"
    [multiple]="true"
    [options]="options()"
    [placeholder]="placeholder()"
    [required]="required()"
    [selectedValues]="selectedValues()"
    [subtitle]="subtitle()"
    [title]="title()"
    (selectedValuesChange)="onSelectionChange($event)">
    @if (selectedValuesTemplate(); as selectedValuesTemplate) {
        <ng-template let-selectedValue="selectedValues" #selectedValueBaseTemplate>
            <ng-container [ngTemplateOutlet]="selectedValuesTemplate" [ngTemplateOutletContext]="{ selectedValues }"></ng-container>
        </ng-template>
    }
    @if (optionTemplate(); as optionTemplate) {
        <ng-template let-option="option" #optionBaseTemplate>
            <ng-container [ngTemplateOutlet]="optionTemplate" [ngTemplateOutletContext]="{ option }"></ng-container>
        </ng-template>
    }
</app-base-select>
