import { BaseSelectComponent } from ':shared/components/forms/selects/base-select/base-select.component';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, contentChild, input, model, output, TemplateRef } from '@angular/core';

@Component({
    selector: 'app-simple-select',
    templateUrl: './simple-select.component.html',
    styleUrls: ['./simple-select.component.scss'],
    standalone: true,
    imports: [NgTemplateOutlet, BaseSelectComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SimpleSelectComponent<T> {
    /**
     * INPUTS
     */
    readonly compareWith = input<(o1: T, o2: T) => boolean>();
    readonly disabled = input<boolean>(false);
    readonly displayWith = input<(o: T) => string>();
    readonly id = input<string>('simple-select-id');
    readonly options = input<T[]>([]);
    readonly placeholder = input<string>('');
    readonly required = input<boolean>(false);
    readonly selectedValue = model<T | null>(null);
    readonly subtitle = input<string>('');
    readonly title = input<string>('');

    /**
     * OUTPUTS
     */
    readonly selectedValueChange = output<T | null>();

    /**
     * CONTENT CHILDREN
     */
    readonly optionTemplate = contentChild<TemplateRef<unknown>>('optionTemplate');
    readonly selectedValueTemplate = contentChild<TemplateRef<unknown>>('selectedValueTemplate');

    /**
     * METHODS
     */
    onSelectionChange(event: T | null): void {
        this.selectedValueChange.emit(event);
    }
}
