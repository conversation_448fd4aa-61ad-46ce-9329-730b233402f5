import { BaseSelectComponent } from ':shared/components/forms/selects/base-select/base-select.component';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, contentChild, input, model, output, TemplateRef } from '@angular/core';

@Component({
    selector: 'app-multi-select',
    templateUrl: './multi-select.component.html',
    styleUrls: ['./multi-select.component.scss'],
    standalone: true,
    imports: [NgTemplateOutlet, BaseSelectComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiSelectComponent<T> {
    /**
     * INPUTS
     */
    readonly compareWith = input<(o1: T, o2: T) => boolean>();
    readonly disabled = input<boolean>(false);
    readonly displayWith = input<(o: T) => string>();
    readonly id = input<string>('multi-select-id');
    readonly options = input<T[]>([]);
    readonly placeholder = input<string>('');
    readonly required = input<boolean>(false);
    readonly selectedValues = model<T[]>([]);
    readonly subtitle = input<string>('');
    readonly title = input<string>('');

    /**
     * OUTPUTS
     */
    readonly selectedValuesChange = output<T[]>();

    /**
     * CONTENT CHILDREN
     */
    readonly optionTemplate = contentChild<TemplateRef<unknown>>('optionTemplate');
    readonly selectedValuesTemplate = contentChild<TemplateRef<unknown>>('selectedValuesTemplate');

    /**
     * METHODS
     */
    onSelectionChange(event: T[]): void {
        this.selectedValuesChange.emit(event);
    }
}
