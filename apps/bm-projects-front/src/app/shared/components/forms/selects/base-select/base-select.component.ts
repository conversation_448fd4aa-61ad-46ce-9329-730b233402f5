import { ExecPipe } from ':shared/pipes/exec.pipe';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, contentChild, input, model, output, TemplateRef } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import _ from 'lodash';

@Component({
    selector: 'app-base-select',
    templateUrl: './base-select.component.html',
    styleUrls: ['./base-select.component.scss'],
    standalone: true,
    imports: [NgTemplateOutlet, MatFormFieldModule, MatSelectModule, ExecPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseSelectComponent<T> {
    /**
     * INPUTS
     */
    readonly compareWith = input<((o1: T, o2: T) => boolean) | undefined>();
    readonly disabled = input<boolean>(false);
    readonly displayWith = input<((o: T) => string) | undefined>();
    readonly id = input<string>('base-select-id');
    readonly multiple = input<boolean>(false);
    readonly options = input<T[]>([]);
    readonly placeholder = input<string>('');
    readonly required = input<boolean>(false);
    readonly selectedValue = model<T | null>(null);
    readonly selectedValues = model<T[]>([]);
    readonly subtitle = input<string>('');
    readonly title = input<string>('');

    /**
     * OUTPUTS
     */
    readonly selectedValueChange = output<T | null>();
    readonly selectedValuesChange = output<T[]>();

    /**
     * CONTENT CHILDREN
     */
    readonly optionBaseTemplate = contentChild<TemplateRef<unknown>>('optionBaseTemplate');
    readonly selectedValueBaseTemplate = contentChild<TemplateRef<unknown>>('selectedValueBaseTemplate');

    /**
     * PROPERTIES
     */
    readonly value = computed((): T[] | T | null => (this.multiple() ? this.selectedValues() : this.selectedValue()));
    readonly computedCompareWith = computed(() => this.compareWith() ?? this.defaulCompareWith);
    readonly computedDisplayWith = computed(() => this.displayWith() ?? this.defaultDisplayWith);

    /**
     * DEFAULT VALUES
     */
    readonly defaulCompareWith = (o1: T, o2: T) => _.isEqual(o1, o2);
    readonly defaultDisplayWith = (o: T) => JSON.stringify(o);

    /**
     * METHODS
     */
    onSelectionChange(selectionChange: MatSelectChange): void {
        if (this.multiple()) {
            this.selectedValuesChange.emit(selectionChange.value);
        } else {
            this.selectedValueChange.emit(selectionChange.value);
        }
    }
}
