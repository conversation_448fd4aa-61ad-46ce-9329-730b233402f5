<app-base-select
    [id]="id()"
    [compareWith]="compareWith()"
    [displayWith]="displayWith()"
    [disabled]="disabled()"
    [options]="options()"
    [placeholder]="placeholder()"
    [required]="required()"
    [selectedValue]="selectedValue()"
    [subtitle]="subtitle()"
    [title]="title()"
    (selectedValueChange)="onSelectionChange($event)">
    @if (selectedValueTemplate(); as selectedValueTemplate) {
        <ng-template let-selectedValue="selectedValue" #selectedValueBaseTemplate>
            <ng-container [ngTemplateOutlet]="selectedValueTemplate" [ngTemplateOutletContext]="{ selectedValue }"></ng-container>
        </ng-template>
    }
    @if (optionTemplate(); as optionTemplate) {
        <ng-template let-option="option" #optionBaseTemplate>
            <ng-container [ngTemplateOutlet]="optionTemplate" [ngTemplateOutletContext]="{ option }"></ng-container>
        </ng-template>
    }
</app-base-select>
