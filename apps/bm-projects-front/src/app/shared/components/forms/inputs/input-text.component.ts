import { ChangeDetectionStrategy, Component, DestroyRef, effect, forwardRef, inject, input, model, output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { isEqual } from 'lodash';
import { debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
    selector: 'app-input-text',
    templateUrl: './input-text.component.html',
    styleUrls: ['./input-text.component.scss'],
    standalone: true,
    imports: [MatInputModule, FormsModule, ReactiveFormsModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => InputTextComponent),
        },
    ],
})
export class InputTextComponent {
    readonly value = model<string | null>('');
    readonly placeholder = input<string>('');
    readonly title = input<string>('');
    readonly subtitle = input<string>('');
    readonly required = input<boolean>(false);
    readonly disabled = input<boolean>(false);

    readonly isValid = output<boolean>();

    private readonly _destroyRef = inject(DestroyRef);

    readonly control = new FormControl<string | null>(null);

    constructor() {
        this.control.valueChanges
            .pipe(
                takeUntilDestroyed(this._destroyRef),
                debounceTime(200),
                distinctUntilChanged((previousValue, currentValue) => isEqual(previousValue, currentValue))
            )
            .subscribe((value) => this.onValueChange(value));

        effect(() => {
            this.control.setValidators(this.required() ? [Validators.required] : []);
        });

        effect(() => {
            if (this.disabled()) {
                this.control.disable();
            } else {
                this.control.enable();
            }
        });

        effect(() => {
            this.control.setValue(this.value());
        });
    }

    onValueChange(value: string | null): void {
        this.value.set(value);
        this.isValid.emit(this.control.valid);
    }
}
