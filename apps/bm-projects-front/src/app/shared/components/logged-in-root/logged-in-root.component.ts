import { GlobalFooterComponent } from ':shared/components/global-footer/global-footer.component';
import { GlobalHeaderComponent } from ':shared/components/global-header/global-header.component';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-logged-in-root',
    templateUrl: './logged-in-root.component.html',
    styleUrls: ['./logged-in-root.component.scss'],
    standalone: true,
    imports: [RouterOutlet, GlobalHeaderComponent, GlobalFooterComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoggedInRootComponent {}
