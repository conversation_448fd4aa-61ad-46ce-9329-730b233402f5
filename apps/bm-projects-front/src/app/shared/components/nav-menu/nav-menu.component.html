<!-- eslint-disable @angular-eslint/template/interactive-supports-focus -->
<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<ul class="menu bg-base-200 rounded-box w-56">
  @for (menuOption of menuOptions(); track menuOption;) {
  <li>
    @if (menuOption.children) {
    <ng-container
      [ngTemplateOutlet]="detailsTemplate"
      [ngTemplateOutletContext]="{ menuOption }"
    ></ng-container>
    } @else {
    <a (click)="menuOption.onClick?.()">{{ menuOption.text }}</a>
    }
  </li>
  }
</ul>

<ng-template #detailsTemplate let-menuOption="menuOption">
  <details open>
    <summary (click)="menuOption.onClick?.()">{{ menuOption.text }}</summary>
    <ul>
      <li>
        @for (menuOptionChild of menuOption.children; track menuOptionChild) {
        @if (menuOptionChild.children) {
        <ng-container
          [ngTemplateOutlet]="detailsTemplate"
          [ngTemplateOutletContext]="{ menuOption: menuOptionChild }"
        ></ng-container>
        } @else {
        <a (click)="menuOptionChild.onClick?.()">{{ menuOptionChild.text }}</a>
        } }
      </li>
    </ul>
  </details>
</ng-template>
