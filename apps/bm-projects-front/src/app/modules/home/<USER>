<div>
  <app-card
    [title]="'home.design_system.title' | translate"
    [text]="'home.design_system.text' | translate"
    [actions]="[
      {
        text: ('common.shared.see_more' | translate),
        click: onGoToComponentsDisplay
      }
    ]"
  ></app-card>

  <app-button [text]="'Test'"></app-button>
  <app-card>
    <div>{{ 'home.selects.title' | translate }}</div>

    <hr />

    <div>{{ 'home.selects.simple_select.title' | translate }}</div>
    <app-simple-select
      [(selectedValue)]="simpleSelectedValue"
      [title]="'home.selects.simple_select.title' | translate"
      [subtitle]="'home.selects.simple_select.subtitle' | translate"
      [options]="simpleSelectOptions()"
      [displayWith]="simpleSelectDisplayWith"
    ></app-simple-select>
    <div>
      <span>{{ 'home.selects.simple_select.selected_value' | translate }}</span>
      <span>{{ simpleSelectDisplayWith | exec : simpleSelectedValue() }}</span>
    </div>

    <hr />

    <div>{{ 'home.selects.multi_select.title' | translate }}</div>
    <app-multi-select
      [(selectedValues)]="multiSelectedValues"
      [title]="'home.selects.multi_select.title' | translate"
      [subtitle]="'home.selects.multi_select.subtitle' | translate"
      [options]="multiSelectOptions()"
      [displayWith]="multiSelectDisplayWith"
    ></app-multi-select>
    <div>
      <span>{{ 'home.selects.multi_select.selected_values' | translate }}</span>
      <span>{{
        multiSelectedValues() | map : multiSelectDisplayWith | join
      }}</span>
    </div>
  </app-card>
</div>
