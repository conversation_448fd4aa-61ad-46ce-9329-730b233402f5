import {
  HomeMultiSelectOption,
  HomeSimpleSelectOption,
} from ':modules/home/<USER>';
import { CardComponent } from ':shared/components/card/card.component';
import { MultiSelectComponent } from ':shared/components/forms/selects/multi-select/multi-select.component';
import { SimpleSelectComponent } from ':shared/components/forms/selects/simple-select/simple-select.component';
import { ExecPipe } from ':shared/pipes/exec.pipe';
import { JoinPipe } from ':shared/pipes/join.pipe';
import { MapPipe } from ':shared/pipes/map.pipe';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  signal,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { ButtonComponent } from ':shared/components/button/button.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    TranslateModule,
    ButtonComponent,
    CardComponent,
    MultiSelectComponent,
    SimpleSelectComponent,
    ExecPipe,
    Join<PERSON>ip<PERSON>,
    MapPipe,
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeComponent {
  private readonly _router = inject(Router);

  readonly simpleSelectedValue = signal<HomeSimpleSelectOption | null>(null);
  readonly simpleSelectOptions = signal([
    { text: 'Option 1', id: 1 },
    { text: 'Option 2', id: 2 },
    { text: 'Option 3', id: 3 },
  ]);
  readonly multiSelectedValues = signal<HomeSimpleSelectOption[]>([]);
  readonly multiSelectOptions = signal([
    { text: 'Option 1', id: 1 },
    { text: 'Option 2', id: 2 },
    { text: 'Option 3', id: 3 },
  ]);

  simpleSelectDisplayWith = (option: HomeSimpleSelectOption | null) =>
    option?.text ?? '-';
  multiSelectDisplayWith = (option: HomeMultiSelectOption) => option.text;

  onGoToComponentsDisplay = () => {
    this._router.navigate(['components']);
  };
}
