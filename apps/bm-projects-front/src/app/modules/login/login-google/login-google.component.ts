import { AuthService } from ':core/services/auth.service';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';

@Component({
    selector: 'app-login-google',
    animations: [
        trigger('inOutAnimation', [
            transition(':enter', [style({ height: 0, opacity: 0 }), animate('0.2s ease-out', style({ height: '*', opacity: 1 }))]),
            transition(':leave', [style({ height: '*', opacity: 1 }), animate('0.2s ease-in', style({ height: 0, opacity: 0 }))]),
        ]),
    ],
    templateUrl: './login-google.component.html',
    styleUrls: ['./login-google.component.scss'],
    imports: [MatButtonModule, MatCardModule, ImagePathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class LoginGoogleComponent {
    readonly showInfo = signal(false);

    private readonly _authService = inject(AuthService);

    login(): void {
        this._authService.loginWithGoogle().subscribe({
            next: ({ redirectUrl }) => {
                window.location.href = redirectUrl;
            },
        });
    }

    toggleInfo(): void {
        this.showInfo.update((showInfo) => !showInfo);
    }
}
