import { environment } from ':environments/environment';
import { LoginGoogleComponent } from ':modules/login/login-google/login-google.component';
import { AuthMethod } from ':modules/login/login.interface';
import { ChangeDetectionStrategy, Component } from '@angular/core';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [LoginGoogleComponent],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginComponent {
    readonly AuthMethod = AuthMethod;
    authMethod = AuthMethod.GOOGLE;

    constructor() {
        this.authMethod = this._getAuthMethod();
    }

    private _getAuthMethod(): AuthMethod {
        const authMethod = environment.authMethod;
        if (authMethod && Object.values(AuthMethod).includes(authMethod as AuthMethod)) {
            return authMethod as AuthMethod;
        }
        return AuthMethod.GOOGLE;
    }
}
