import { UsersService } from ':core/services/users.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { TableComponent } from ':shared/components/table/table.component';
import { TableColumn } from ':shared/components/table/table.interface';
import { User } from ':shared/models/users.model';
import { JsonPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';

@Component({
    selector: 'app-users-management',
    templateUrl: './users-management.component.html',
    styleUrls: ['./users-management.component.scss'],
    standalone: true,
    imports: [ButtonComponent, TableComponent, JsonPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UsersManagementComponent implements OnInit {
    private readonly _usersService = inject(UsersService);

    readonly users = signal<User[]>([]);
    readonly DISPLAYED_COLUMNS: TableColumn<User>[] = [
        {
            key: 'name',
            header: 'Name',
            displayCellWith: (user) => `${user.firstname} ${user.lastname}`,
            sortable: true,
            searchable: true,
        },
        {
            key: 'email',
            header: 'Email',
            displayCellWith: (user) => user.email,
            sortable: true,
            searchable: true,
        },
        {
            key: 'role',
            header: 'Roles',
            displayCellWith: (user) => user.roles.join(', '),
            sortable: true,
            searchable: true,
        },
    ];

    ngOnInit(): void {
        this._usersService.getAllUsers().subscribe((users) => {
            this.users.set(users);
        });
    }
}
