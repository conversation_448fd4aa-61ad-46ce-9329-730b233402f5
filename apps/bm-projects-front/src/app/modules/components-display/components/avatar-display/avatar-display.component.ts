import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AvatarComponent } from ':shared/components/avatar/avatar.component';
import { CardComponent } from ':shared/components/card/card.component';

@Component({
  selector: 'app-avatar-display',
  templateUrl: './avatar-display.component.html',
  styleUrls: ['./avatar-display.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AvatarComponent, CardComponent],
})
export class AvatarDisplayComponent {}
