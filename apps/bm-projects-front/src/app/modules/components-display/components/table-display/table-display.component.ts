import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TableComponent } from ':shared/components/table/table.component';
import { CardComponent } from ':shared/components/card/card.component';
import { TableColumn } from ':shared/components/table/table.interface';
import { User } from ':shared/models/users.model';
import { Role } from ':shared/enums/role';

@Component({
  selector: 'app-table-display',
  templateUrl: './table-display.component.html',
  styleUrls: ['./table-display.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TableComponent, CardComponent],
})
export class TableDisplayComponent {
  readonly displayedColumns: TableColumn<User>[] = [
    {
      key: 'name',
      header: 'Name',
      displayCellWith: (user) => `${user.firstname} ${user.lastname}`,
      sortable: true,
      searchable: false,
    },
    {
      key: 'email',
      header: 'Email',
      displayCellWith: (user) => user.email,
      sortable: true,
      searchable: false,
    },
    {
      key: 'role',
      header: 'Roles',
      displayCellWith: (user) => user.roles.join(', '),
      sortable: true,
      searchable: false,
    },
  ];

  readonly data: User[] = [
    new User({
      id: '1',
      email: '<EMAIL>',
      firstname: 'John',
      lastname: 'Doe',
      active: true,
      roles: [Role.USER],
    }),
    new User({
      id: '2',
      email: '<EMAIL>',
      firstname: 'Clara',
      lastname: 'Hale',
      active: true,
      roles: [Role.USER, Role.ADMIN],
    }),
    new User({
      id: '3',
      email: '<EMAIL>',
      firstname: 'Gilbert',
      lastname: 'Mason',
      active: false,
      roles: [Role.USER],
    }),
    new User({
      id: '4',
      email: '<EMAIL>',
      firstname: 'John',
      lastname: 'Smith',
      active: true,
      roles: [Role.USER],
    }),
    new User({
      id: '5',
      email: '<EMAIL>',
      firstname: 'LeBron',
      lastname: 'James',
      active: true,
      roles: [Role.USER],
    }),
  ];
}
