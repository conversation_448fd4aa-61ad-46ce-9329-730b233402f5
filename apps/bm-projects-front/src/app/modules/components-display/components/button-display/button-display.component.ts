import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ButtonComponent } from ':shared/components/button/button.component';
import { CardComponent } from ':shared/components/card/card.component';

@Component({
  selector: 'app-button-display',
  templateUrl: './button-display.component.html',
  styleUrls: ['./button-display.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ButtonComponent, CardComponent],
})
export class ButtonDisplayComponent {}
