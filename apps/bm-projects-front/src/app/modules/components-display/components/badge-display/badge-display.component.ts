import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BadgeComponent } from ':shared/components/badge/badge.component';
import { CardComponent } from ':shared/components/card/card.component';

@Component({
  selector: 'app-badge-display',
  templateUrl: './badge-display.component.html',
  styleUrls: ['./badge-display.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BadgeComponent, CardComponent],
})
export class BadgeDisplayComponent {}
