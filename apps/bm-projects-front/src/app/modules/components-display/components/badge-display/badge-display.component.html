<app-card [title]="'Badge'" id="badge">
  <div class="flex flex-col gap-6">
    <div>
      <div class="divider font-bold">NORMAL</div>

      <div class="flex gap-2 flex-wrap">
        <app-badge [text]="'Primary'"></app-badge>
        <app-badge [text]="'Secondary'" [type]="'secondary'"></app-badge>
        <app-badge [text]="'Neutral'" [type]="'neutral'"></app-badge>
        <app-badge [text]="'Accent'" [type]="'accent'"></app-badge>
        <app-badge [text]="'Info'" [type]="'info'"></app-badge>
        <app-badge [text]="'Success'" [type]="'success'"></app-badge>
        <app-badge [text]="'Warning'" [type]="'warning'"></app-badge>
        <app-badge [text]="'Error'" [type]="'error'"></app-badge>
      </div>
    </div>

    <div>
      <div class="divider font-bold">SOFT</div>

      <div class="flex gap-2 flex-wrap">
        <app-badge [text]="'Primary'" [category]="'soft'"></app-badge>
        <app-badge
          [text]="'Secondary'"
          [type]="'secondary'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Neutral'"
          [type]="'neutral'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Accent'"
          [type]="'accent'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Info'"
          [type]="'info'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Success'"
          [type]="'success'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Warning'"
          [type]="'warning'"
          [category]="'soft'"
        ></app-badge>
        <app-badge
          [text]="'Error'"
          [type]="'error'"
          [category]="'soft'"
        ></app-badge>
      </div>
    </div>

    <div>
      <div class="divider font-bold">OUTLINE</div>

      <div class="flex gap-2 flex-wrap">
        <app-badge [text]="'Primary'" [category]="'outline'"></app-badge>
        <app-badge
          [text]="'Secondary'"
          [type]="'secondary'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Neutral'"
          [type]="'neutral'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Accent'"
          [type]="'accent'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Info'"
          [type]="'info'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Success'"
          [type]="'success'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Warning'"
          [type]="'warning'"
          [category]="'outline'"
        ></app-badge>
        <app-badge
          [text]="'Error'"
          [type]="'error'"
          [category]="'outline'"
        ></app-badge>
      </div>
    </div>

    <div>
      <div class="divider font-bold">DASH</div>

      <div class="flex gap-2 flex-wrap">
        <app-badge [text]="'Primary'" [category]="'dash'"></app-badge>
        <app-badge
          [text]="'Secondary'"
          [type]="'secondary'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Neutral'"
          [type]="'neutral'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Accent'"
          [type]="'accent'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Info'"
          [type]="'info'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Success'"
          [type]="'success'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Warning'"
          [type]="'warning'"
          [category]="'dash'"
        ></app-badge>
        <app-badge
          [text]="'Error'"
          [type]="'error'"
          [category]="'dash'"
        ></app-badge>
      </div>
    </div>

    <div>
      <div class="divider font-bold">GHOST</div>

      <div class="flex gap-2 flex-wrap">
        <app-badge [text]="'Primary'" [category]="'ghost'"></app-badge>
        <app-badge
          [text]="'Secondary'"
          [type]="'secondary'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Neutral'"
          [type]="'neutral'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Accent'"
          [type]="'accent'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Info'"
          [type]="'info'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Success'"
          [type]="'success'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Warning'"
          [type]="'warning'"
          [category]="'ghost'"
        ></app-badge>
        <app-badge
          [text]="'Error'"
          [type]="'error'"
          [category]="'ghost'"
        ></app-badge>
      </div>
    </div>

    <div>
      <div class="divider font-bold">SIZES</div>

      <div class="flex gap-2 items-center flex-wrap">
        <app-badge [text]="'Extra Small'" [size]="'xs'"></app-badge>
        <app-badge [text]="'Small'" [size]="'sm'"></app-badge>
        <app-badge [text]="'Medium'" [size]="'md'"></app-badge>
        <app-badge [text]="'Large'" [size]="'lg'"></app-badge>
        <app-badge [text]="'Extra Large'" [size]="'xl'"></app-badge>
      </div>
    </div>
  </div>
</app-card>
