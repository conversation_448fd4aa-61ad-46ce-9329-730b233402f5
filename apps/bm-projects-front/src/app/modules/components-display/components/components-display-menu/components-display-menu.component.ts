import {
  NavMenuComponent,
  NavMenuOption,
} from ':shared/components/nav-menu/nav-menu.component';
import { ChangeDetectionStrategy, Component } from '@angular/core';

@Component({
  selector: 'app-components-display-menu',
  templateUrl: './components-display-menu.component.html',
  styleUrls: ['./components-display-menu.component.scss'],
  standalone: true,
  imports: [NavMenuComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComponentsDisplayMenuComponent {
  readonly menuOptions: NavMenuOption[] = [
    { text: 'Avatar', onClick: () => this.goToId('avatar') },
    { text: 'Badge', onClick: () => this.goToId('badge') },
    { text: 'Button', onClick: () => this.goToId('button') },
    { text: 'Table', onClick: () => this.goToId('table') },
    {
      text: 'Parent',
      children: [
        { text: 'Submenu 1', onClick: () => console.log('Submenu 1') },
        { text: 'Submenu 2' },
        {
          text: 'Parent',
          children: [{ text: 'Submenu 1' }, { text: 'Submenu 2' }],
        },
      ],
      onClick: () => console.log('Parent'),
    },
    { text: 'Item 3' },
  ];

  goToId = (id: string): void => {
    document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
  };
}
