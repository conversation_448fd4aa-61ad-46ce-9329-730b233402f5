import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ComponentsDisplayMenuComponent } from ':modules/components-display/components/components-display-menu/components-display-menu.component';
import { AvatarDisplayComponent } from ':modules/components-display/components/avatar-display/avatar-display.component';
import { BadgeDisplayComponent } from ':modules/components-display/components/badge-display/badge-display.component';
import { ButtonDisplayComponent } from ':modules/components-display/components/button-display/button-display.component';
import { TableDisplayComponent } from ':modules/components-display/components/table-display/table-display.component';

@Component({
  selector: 'app-components-display',
  templateUrl: './components-display.component.html',
  styleUrls: ['./components-display.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AvatarDisplayComponent,
    BadgeDisplayComponent,
    ButtonDisplayComponent,
    ComponentsDisplayMenuComponent,
    TableDisplayComponent,
  ],
})
export class ComponentsDisplayComponent {}
