{"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/utils/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/utils", "main": "packages/utils/src/index.ts", "tsConfig": "packages/utils/tsconfig.lib.json", "assets": ["packages/utils/*.md"]}}}}