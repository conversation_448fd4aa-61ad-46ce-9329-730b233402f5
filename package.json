{"name": "@bm-projects-monorepo/source", "version": "0.0.0", "license": "MIT", "packageManager": "pnpm@10.6.5", "engines": {"node": "^22", "pnpm": "~10.6"}, "pnpm": {"supportedArchitectures": {"cpu": ["x64", "arm64"], "os": ["win32", "darwin", "current"]}}, "scripts": {"start-dev": "nx run-many --target=serve --projects=bm-projects-front,bm-projects-back --configuration=development"}, "private": true, "dependencies": {"@angular/animations": "^19.2.5", "@angular/common": "~19.2.2", "@angular/compiler": "~19.2.2", "@angular/core": "~19.2.2", "@angular/forms": "~19.2.2", "@angular/material": "^19.2.8", "@angular/platform-browser": "~19.2.2", "@angular/platform-browser-dynamic": "~19.2.2", "@angular/router": "~19.2.2", "@auth0/angular-jwt": "^5.2.0", "@ngrx/operators": "^19.1.0", "@ngrx/signals": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@tailwindcss/postcss": "^4.1.3", "axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.21.2", "json-schema-to-ts": "^3.1.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.13.2", "postcss": "^8.5.3", "rxjs": "~7.8.0", "tailwindcss": "^4.1.3", "tsyringe": "^4.9.1", "utility-types": "^3.11.0", "winston": "^3.17.0", "zod": "^3.24.2", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.2.2", "@angular-devkit/core": "~19.2.2", "@angular-devkit/schematics": "~19.2.2", "@angular/cli": "~19.2.2", "@angular/compiler-cli": "~19.2.2", "@angular/language-service": "~19.2.2", "@eslint/js": "^9.8.0", "@nx/angular": "20.7.1", "@nx/devkit": "20.7.1", "@nx/eslint": "20.7.1", "@nx/eslint-plugin": "20.7.1", "@nx/express": "^20.7.1", "@nx/jest": "20.7.1", "@nx/js": "20.7.1", "@nx/node": "20.7.1", "@nx/playwright": "20.7.1", "@nx/vite": "20.7.1", "@nx/web": "20.7.1", "@nx/webpack": "20.7.1", "@nx/workspace": "20.7.1", "@playwright/test": "^1.36.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@schematics/angular": "~19.2.2", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/node": "18.16.9", "@types/uuid": "^10.0.0", "@typescript-eslint/utils": "^8.19.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "angular-eslint": "^19.2.2", "daisyui": "^5.0.27", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-preset-angular": "~14.4.0", "jiti": "2.4.2", "jsonc-eslint-parser": "^2.1.0", "nx": "20.7.1", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "ts-toolbelt": "^9.6.0", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.0", "vitest": "^3.0.0", "webpack-cli": "^5.1.4"}}